import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Modal, ScrollView, TouchableOpacity, TextInput, Platform } from 'react-native';
import { X, Calendar } from 'lucide-react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
import { useQuery } from '@tanstack/react-query';
import {
  fetchListingTypes,
  fetchBedrooms,
  fetchBathrooms,
  fetchKitchens,
  fetchPaymentMethods,
  fetchMarketingPlatforms,
  fetchAmenities,
  fetchListingViews,
  fetchNationalities
} from '@/lib/api';
import Button from '@/components/Button';
import Dropdown from '@/components/Dropdown';
// Helper function to transform API data to dropdown format
const transformApiDataToDropdownOptions = (data: any[]) => {
  if (!Array.isArray(data)) return [];

  return data.map((item) => ({
    id: item.id?.toString() || '',
    label: item.name || item.title || item.label || 'Unknown',
    value: item,
  }));
};

interface RequestData {
  rentSale: string;
  locations: string;
  towerBuilding: string;
  propertyType: string[];
  parking: string;
  minArea: string;
  maxArea: string;
  furnishing: string;
  furnishingOffice: string;
  balcony: string;
  views: string;
  pantry: string;
  bathrooms: string;
  priceMin: string;
  priceMax: string;
  amenities: string;
  exclusiveProperty: boolean;
  marketingPlatforms: string;
  decisionDate: string;
  inquiredRefNo: string;
  specificRequirements: string;
}

interface RequestModalProps {
  visible: boolean;
  onClose: () => void;
  onSave: (data: RequestData) => void;
  initialData?: Partial<RequestData>;
}

export default function RequestModal({ visible, onClose, onSave, initialData }: RequestModalProps) {
  const [formData, setFormData] = useState<RequestData>({
    rentSale: initialData?.rentSale || '',
    locations: initialData?.locations || '',
    towerBuilding: initialData?.towerBuilding || '',
    propertyType: initialData?.propertyType || [],
    parking: initialData?.parking || '',
    minArea: initialData?.minArea || '',
    maxArea: initialData?.maxArea || '',
    furnishing: initialData?.furnishing || '',
    furnishingOffice: initialData?.furnishingOffice || '',
    balcony: initialData?.balcony || '',
    views: initialData?.views || '',
    pantry: initialData?.pantry || '',
    bathrooms: initialData?.bathrooms || '',
    priceMin: initialData?.priceMin || '',
    priceMax: initialData?.priceMax || '',
    amenities: initialData?.amenities || '',
    exclusiveProperty: initialData?.exclusiveProperty || false,
    marketingPlatforms: initialData?.marketingPlatforms || '',
    decisionDate: initialData?.decisionDate || '',
    inquiredRefNo: initialData?.inquiredRefNo || '',
    specificRequirements: initialData?.specificRequirements || '',
  });

  const [showDatePicker, setShowDatePicker] = useState(false);

  // Update formData when initialData changes
  useEffect(() => {
    console.log('RequestModal - initialData changed:', initialData);
    if (initialData) {
      console.log('RequestModal - setting formData with rentSale:', initialData.rentSale);
      setFormData({
        rentSale: initialData?.rentSale || '',
        locations: initialData?.locations || '',
        towerBuilding: initialData?.towerBuilding || '',
        propertyType: initialData?.propertyType || [],
        parking: initialData?.parking || '',
        minArea: initialData?.minArea || '',
        maxArea: initialData?.maxArea || '',
        furnishing: initialData?.furnishing || '',
        furnishingOffice: initialData?.furnishingOffice || '',
        balcony: initialData?.balcony || '',
        views: initialData?.views || '',
        pantry: initialData?.pantry || '',
        bathrooms: initialData?.bathrooms || '',
        priceMin: initialData?.priceMin || '',
        priceMax: initialData?.priceMax || '',
        amenities: initialData?.amenities || '',
        exclusiveProperty: initialData?.exclusiveProperty || false,
        marketingPlatforms: initialData?.marketingPlatforms || '',
        decisionDate: initialData?.decisionDate || '',
        inquiredRefNo: initialData?.inquiredRefNo || '',
        specificRequirements: initialData?.specificRequirements || '',
      });
    }
  }, [initialData]);

  // Fetch dropdown data
  const { data: listingTypes = [] } = useQuery({
    queryKey: ['listingTypes'],
    queryFn: fetchListingTypes,
  });

  const { data: bedrooms = [] } = useQuery({
    queryKey: ['bedrooms'],
    queryFn: fetchBedrooms,
  });

  const { data: bathrooms = [] } = useQuery({
    queryKey: ['bathrooms'],
    queryFn: fetchBathrooms,
  });

  const { data: kitchens = [] } = useQuery({
    queryKey: ['kitchens'],
    queryFn: fetchKitchens,
  });

  const { data: paymentMethods = [] } = useQuery({
    queryKey: ['paymentMethods'],
    queryFn: fetchPaymentMethods,
  });

  const { data: marketingPlatforms = [] } = useQuery({
    queryKey: ['marketingPlatforms'],
    queryFn: fetchMarketingPlatforms,
  });

  const { data: amenities = [] } = useQuery({
    queryKey: ['amenities'],
    queryFn: fetchAmenities,
  });

  const { data: listingViews = [] } = useQuery({
    queryKey: ['listingViews'],
    queryFn: fetchListingViews,
  });

  const handleSave = () => {
    onSave(formData);
    onClose();
  };

  const updateField = (field: keyof RequestData, value: any) => {
    console.log(`Updating field ${field} with value:`, value);
    setFormData(prev => {
      const newData = { ...prev, [field]: value };
      console.log('New formData:', newData);
      return newData;
    });
  };

  // Date picker functions
  const handleDatePress = () => {
    setShowDatePicker(true);
  };

  const handleDateChange = (event: any, selectedDate?: Date) => {
    setShowDatePicker(false);

    if (selectedDate) {
      const formattedDate = selectedDate.toLocaleDateString('en-GB', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
      });
      updateField('decisionDate', formattedDate);
    }
  };

  // Mock options - in real app, these would come from API
  const rentSaleOptions = [
    { id: 'rent', label: 'Rent' },
    { id: 'sale', label: 'Sale' },
  ];

  const locationOptions = [
    { id: 'doha', label: 'Doha' },
    { id: 'west_bay', label: 'West Bay' },
    { id: 'pearl', label: 'The Pearl' },
  ];

  // Transform API data to dropdown format
  const propertyTypeOptions = transformApiDataToDropdownOptions(listingTypes);

  const parkingOptions = [
    { id: 'none', label: 'Please select' },
    { id: '1', label: '1 Space' },
    { id: '2', label: '2 Spaces' },
    { id: '3+', label: '3+ Spaces' },
  ];

  const furnishingOptions = [
    { id: 'furnished', label: 'Furnished' },
    { id: 'unfurnished', label: 'Unfurnished' },
    { id: 'semi_furnished', label: 'Semi Furnished' },
  ];

  const bathroomOptions = transformApiDataToDropdownOptions(bathrooms);
  const bedroomOptions = transformApiDataToDropdownOptions(bedrooms);
  const kitchenOptions = transformApiDataToDropdownOptions(kitchens);
  const marketingPlatformOptions = transformApiDataToDropdownOptions(marketingPlatforms);
  const amenityOptions = transformApiDataToDropdownOptions(amenities);
  const viewOptions = transformApiDataToDropdownOptions(listingViews);

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="formSheet"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Request</Text>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <X size={24} color="#6B7280" />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          <View style={styles.row}>
            <View style={styles.halfWidth}>
              <Dropdown
                label="Rent/Sale"
                options={rentSaleOptions}
                selectedValue={formData.rentSale}
                onSelect={(option) => updateField('rentSale', option.id)}
                placeholder="Rent"
              />
            </View>
            <View style={styles.halfWidth}>
              <Dropdown
                label="Locations"
                options={locationOptions}
                selectedValue={formData.locations}
                onSelect={(option) => updateField('locations', option.id)}
                placeholder="Select"
              />
            </View>
          </View>

          <View style={styles.row}>
            <View style={styles.halfWidth}>
              <View style={styles.inputContainer}>
                <Text style={styles.label}>Tower/Building</Text>
                <TextInput
                  style={styles.input}
                  value={formData.towerBuilding}
                  onChangeText={(text) => updateField('towerBuilding', text)}
                  placeholder="Please select a location"
                  placeholderTextColor="#9CA3AF"
                />
              </View>
            </View>
            <View style={styles.halfWidth}>
              <View style={styles.inputContainer}>
                <Text style={styles.label}>Property Type</Text>
                <TextInput
                  style={styles.input}
                  value="Chalet, Townhouse, Office"
                  editable={false}
                  placeholderTextColor="#9CA3AF"
                />
              </View>
            </View>
          </View>

          <View style={styles.row}>
            <View style={styles.halfWidth}>
              <Dropdown
                label="Parking"
                options={parkingOptions}
                selectedValue={formData.parking}
                onSelect={(option) => updateField('parking', option.id)}
                placeholder="Please select"
              />
            </View>
            <View style={styles.halfWidth}>
              <View style={styles.inputContainer}>
                <Text style={styles.label}>Min Area</Text>
                <TextInput
                  style={styles.input}
                  value={formData.minArea}
                  onChangeText={(text) => updateField('minArea', text)}
                  placeholder="Please select"
                  placeholderTextColor="#9CA3AF"
                />
              </View>
            </View>
          </View>

          <View style={styles.row}>
            <View style={styles.halfWidth}>
              <View style={styles.inputContainer}>
                <Text style={styles.label}>Max Area</Text>
                <TextInput
                  style={styles.input}
                  value={formData.maxArea}
                  onChangeText={(text) => updateField('maxArea', text)}
                  placeholder="Please select"
                  placeholderTextColor="#9CA3AF"
                />
              </View>
            </View>
            <View style={styles.halfWidth}>
              <Dropdown
                label="Furnishing"
                options={furnishingOptions}
                selectedValue={formData.furnishing}
                onSelect={(option) => updateField('furnishing', option.id)}
                placeholder="Select"
              />
            </View>
          </View>

          <View style={styles.row}>
            <View style={styles.halfWidth}>
              <View style={styles.inputContainer}>
                <Text style={styles.label}>Furnishing office</Text>
                <TextInput
                  style={styles.input}
                  value={formData.furnishingOffice}
                  onChangeText={(text) => updateField('furnishingOffice', text)}
                  placeholder="Select"
                  placeholderTextColor="#9CA3AF"
                />
              </View>
            </View>
            <View style={styles.halfWidth}>
              <View style={styles.inputContainer}>
                <Text style={styles.label}>Balcony</Text>
                <TextInput
                  style={styles.input}
                  value={formData.balcony}
                  onChangeText={(text) => updateField('balcony', text)}
                  placeholder="Please select"
                  placeholderTextColor="#9CA3AF"
                />
              </View>
            </View>
          </View>

          <View style={styles.row}>
            <View style={styles.halfWidth}>
              <Dropdown
                label="Views"
                options={viewOptions}
                selectedValue={formData.views}
                onSelect={(option) => updateField('views', option.id)}
                placeholder="Select"
              />
            </View>
            <View style={styles.halfWidth}>
              <View style={styles.inputContainer}>
                <Text style={styles.label}>Pantry</Text>
                <TextInput
                  style={styles.input}
                  value={formData.pantry}
                  onChangeText={(text) => updateField('pantry', text)}
                  placeholder="Please select"
                  placeholderTextColor="#9CA3AF"
                />
              </View>
            </View>
          </View>

          <View style={styles.row}>
            <View style={styles.halfWidth}>
              <Dropdown
                label="Bathrooms"
                options={bathroomOptions}
                selectedValue={formData.bathrooms}
                onSelect={(option) => updateField('bathrooms', option.id)}
                placeholder="Please select"
              />
            </View>
            <View style={styles.halfWidth}>
              <View style={styles.inputContainer}>
                <Text style={styles.label}>Price Min</Text>
                <TextInput
                  style={styles.input}
                  value={formData.priceMin}
                  onChangeText={(text) => updateField('priceMin', text)}
                  placeholder="Please select"
                  placeholderTextColor="#9CA3AF"
                />
              </View>
            </View>
          </View>

          <View style={styles.row}>
            <View style={styles.halfWidth}>
              <View style={styles.inputContainer}>
                <Text style={styles.label}>Price Max</Text>
                <TextInput
                  style={styles.input}
                  value={formData.priceMax}
                  onChangeText={(text) => updateField('priceMax', text)}
                  placeholder="Please select"
                  placeholderTextColor="#9CA3AF"
                />
              </View>
            </View>
            <View style={styles.halfWidth}>
              <Dropdown
                label="Amenities"
                options={amenityOptions}
                selectedValue={formData.amenities}
                onSelect={(option) => updateField('amenities', option.id)}
                placeholder="Select"
              />
            </View>
          </View>

          <View style={styles.checkboxRow}>
            <TouchableOpacity
              style={styles.checkbox}
              onPress={() => updateField('exclusiveProperty', !formData.exclusiveProperty)}
            >
              <View style={[styles.checkboxBox, formData.exclusiveProperty && styles.checkboxChecked]} />
              <Text style={styles.checkboxLabel}>Exclusive property / Hot deal</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.inputContainer}>
            <Dropdown
              label="Marketing platforms"
              options={marketingPlatformOptions}
              selectedValue={formData.marketingPlatforms}
              onSelect={(option) => updateField('marketingPlatforms', option.id)}
              placeholder="Select"
            />
          </View>

          <View style={styles.row}>
            <View style={styles.halfWidth}>
              <View style={styles.inputContainer}>
                <Text style={styles.label}>Decision Date</Text>
                <View style={styles.dateInput}>
                  <TextInput
                    style={[styles.input, { flex: 1 }]}
                    value={formData.decisionDate}
                    onChangeText={(text) => updateField('decisionDate', text)}
                    placeholder="dd.mm.yyyy"
                    placeholderTextColor="#9CA3AF"
                  />
                  <TouchableOpacity
                    style={styles.calendarButton}
                    onPress={handleDatePress}
                  >
                    <Calendar size={20} color="#6B7280" />
                  </TouchableOpacity>
                </View>
                {/* Inline Date Picker */}
                {showDatePicker && (
                  <View style={styles.inlineDatePicker}>
                    <DateTimePicker
                      value={(() => {
                        try {
                          return formData.decisionDate ? new Date(formData.decisionDate) : new Date();
                        } catch (error) {
                          return new Date();
                        }
                      })()}
                      mode="date"
                      display="default"
                      onChange={handleDateChange}
                    />
                  </View>
                )}
              </View>
            </View>
            <View style={styles.halfWidth}>
              <View style={styles.inputContainer}>
                <Text style={styles.label}>Inquired Ref No.</Text>
                <TextInput
                  style={[styles.input, styles.textArea]}
                  value={formData.inquiredRefNo}
                  onChangeText={(text) => updateField('inquiredRefNo', text)}
                  placeholder=""
                  placeholderTextColor="#9CA3AF"
                  multiline
                  numberOfLines={4}
                />
              </View>
            </View>
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.label}>Specific Requirements</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={formData.specificRequirements}
              onChangeText={(text) => updateField('specificRequirements', text)}
              placeholder="Enter specific requirements..."
              placeholderTextColor="#9CA3AF"
              multiline
              numberOfLines={4}
            />
          </View>
        </ScrollView>

        <View style={styles.footer}>
          <Button
            label="Save Request"
            onPress={handleSave}
            fullWidth
          />
        </View>

      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: '#111827',
  },
  closeButton: {
    padding: 4,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  row: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 16,
  },
  halfWidth: {
    flex: 1,
  },
  inputContainer: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 8,
  },
  input: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: '#1F2937',
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  dateInput: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingRight: 12,
  },
  calendarIcon: {
    marginLeft: 8,
  },
  checkboxRow: {
    marginBottom: 16,
  },
  checkbox: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  checkboxBox: {
    width: 20,
    height: 20,
    borderWidth: 2,
    borderColor: '#D1D5DB',
    borderRadius: 4,
  },
  checkboxChecked: {
    backgroundColor: '#B89C4C',
    borderColor: '#B89C4C',
  },
  checkboxLabel: {
    fontSize: 16,
    color: '#374151',
  },
  footer: {
    padding: 16,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  // Calendar button styles
  calendarButton: {
    position: 'absolute',
    right: 8,
    top: 0,
    bottom: 0,
    width: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  // Inline date picker styles
  inlineDatePicker: {
    marginTop: 8,
    backgroundColor: 'transparent',
    padding: 0,
  },
});
