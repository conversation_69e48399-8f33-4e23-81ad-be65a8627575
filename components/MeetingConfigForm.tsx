import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Switch,
} from 'react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
import { Calendar } from 'lucide-react-native';

interface MeetingConfig {
  title: string;
  content: string;
  dueDate: string;
  priority: string;
  sendEmail: boolean;
  remarks: string;
}

interface MeetingConfigFormProps {
  meetingConfig: MeetingConfig;
  updateMeetingConfig: (field: keyof MeetingConfig, value: string | boolean) => void;
  handleDatePress: (configType: 'meeting') => void;
}

export default function MeetingConfigForm({
  meetingConfig,
  updateMeetingConfig,
  handleDatePress,
}: MeetingConfigFormProps) {
  const [showDatePicker, setShowDatePicker] = useState(false);

  const handleLocalDatePress = () => {
    setShowDatePicker(true);
  };

  const handleDateChange = (_event: any, selectedDate?: Date) => {
    setShowDatePicker(false);

    if (selectedDate) {
      const formattedDate = selectedDate.toLocaleDateString('en-GB', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
      }) + ', ' + selectedDate.toLocaleTimeString('en-GB', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false,
      });

      updateMeetingConfig('dueDate', formattedDate);
    }
  };
  return (
    <ScrollView
      style={styles.configContainer}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={styles.scrollContent}
    >
      <Text style={styles.configTitle}>Lead configuration</Text>

      {/* Reminder Section */}
      <Text style={styles.sectionTitle}>Reminder</Text>
      
      <View style={styles.inputContainer}>
        <Text style={styles.inputLabel}>Title</Text>
        <TextInput
          style={styles.textInput}
          placeholder="Meeting appointment"
          value={meetingConfig.title}
          onChangeText={(text) => updateMeetingConfig('title', text)}
        />
      </View>

      <View style={styles.inputContainer}>
        <Text style={styles.inputLabel}>Content</Text>
        <TextInput
          style={[styles.textInput, styles.textArea]}
          placeholder="Meeting appointment reminder."
          value={meetingConfig.content}
          onChangeText={(text) => updateMeetingConfig('content', text)}
          multiline
          numberOfLines={3}
        />
      </View>

      <View style={styles.inputContainer}>
        <Text style={styles.inputLabel}>Due Date</Text>
        <View style={styles.dateInputContainer}>
          <TouchableOpacity
            style={styles.dateButton}
            onPress={handleLocalDatePress}
          >
            <Text style={styles.dateButtonText}>
              {meetingConfig.dueDate || 'Select date and time'}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.calendarButton}
            onPress={handleLocalDatePress}
          >
            <Calendar size={20} color="#6B7280" />
          </TouchableOpacity>
        </View>
        {/* Inline Date Picker */}
        {showDatePicker && (
          <View style={styles.inlineDatePicker}>
            <DateTimePicker
              value={new Date()}
              mode="date"
              display="default"
              onChange={handleDateChange}
            />
          </View>
        )}
      </View>

      <View style={styles.inputContainer}>
        <Text style={styles.inputLabel}>Priority</Text>
        <View style={styles.priorityContainer}>
          {['Low', 'Medium', 'High'].map((priority) => (
            <TouchableOpacity
              key={priority}
              style={[
                styles.priorityButton,
                meetingConfig.priority === priority && styles.priorityButtonActive
              ]}
              onPress={() => updateMeetingConfig('priority', priority)}
            >
              <Text
                style={[
                  styles.priorityButtonText,
                  meetingConfig.priority === priority && styles.priorityButtonTextActive
                ]}
              >
                {priority}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      <View style={styles.switchContainer}>
        <Text style={styles.switchLabel}>Send Email</Text>
        <Switch
          value={meetingConfig.sendEmail}
          onValueChange={(value) => updateMeetingConfig('sendEmail', value)}
          trackColor={{ false: '#E5E7EB', true: '#B89C4C' }}
          thumbColor={meetingConfig.sendEmail ? '#fff' : '#fff'}
        />
      </View>

      <View style={styles.inputContainer}>
        <Text style={styles.inputLabel}>Remarks</Text>
        <TextInput
          style={[styles.textInput, styles.textArea]}
          placeholder="Additional remarks..."
          value={meetingConfig.remarks}
          onChangeText={(text) => updateMeetingConfig('remarks', text)}
          multiline
          numberOfLines={3}
        />
      </View>


    </ScrollView>
  );
}

const styles = StyleSheet.create({
  configContainer: {
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    marginTop: 16,
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 32,
  },
  configTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 12,
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    color: '#111827',
    backgroundColor: '#fff',
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  dateInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
  },
  dateButton: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    backgroundColor: '#fff',
  },
  dateButtonText: {
    fontSize: 14,
    color: '#6B7280',
  },
  calendarButton: {
    position: 'absolute',
    right: 8,
    padding: 4,
  },
  inlineDatePicker: {
    marginTop: 8,
    backgroundColor: '#fff',
    borderRadius: 8,
    overflow: 'hidden',
  },
  priorityContainer: {
    flexDirection: 'row',
    gap: 8,
  },
  priorityButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    backgroundColor: '#fff',
  },
  priorityButtonActive: {
    backgroundColor: '#B89C4C',
    borderColor: '#B89C4C',
  },
  priorityButtonText: {
    fontSize: 14,
    color: '#6B7280',
  },
  priorityButtonTextActive: {
    color: '#fff',
  },
  switchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginVertical: 16,
  },
  switchLabel: {
    fontSize: 14,
    color: '#374151',
    fontWeight: '500',
  },

});
