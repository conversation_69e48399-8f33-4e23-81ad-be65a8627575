// import React, { useState, useMemo } from 'react';
// import { View, Text, StyleSheet, TouchableOpacity, Modal, FlatList, TextInput } from 'react-native';
// import { ChevronDown, Check, Search } from 'lucide-react-native';

// interface DropdownOption {
//   id: string | number;
//   label: string;
//   value?: any;
// }

// interface DropdownProps {
//   label?: string;
//   placeholder?: string;
//   options: DropdownOption[];
//   selectedValue?: string | number;
//   onSelect: (option: DropdownOption) => void;
//   error?: string;
//   searchable?: boolean;
//   searchPlaceholder?: string;
// }

// export default function Dropdown({
//   label,
//   placeholder = "Please select",
//   options,
//   selectedValue,
//   onSelect,
//   error,
//   searchable = false,
//   searchPlaceholder = "Search...",
// }: DropdownProps) {
//   const [isVisible, setIsVisible] = useState(false);
//   const [searchQuery, setSearchQuery] = useState('');

//   const selectedOption = options.find(option => option.id === selectedValue);

//   // Debug logging
//   console.log('Dropdown - selectedValue:', selectedValue);
//   console.log('Dropdown - options:', options);
//   console.log('Dropdown - selectedOption:', selectedOption);

//   // Filter options based on search query
//   const filteredOptions = useMemo(() => {
//     if (!searchable || !searchQuery.trim()) {
//       return options;
//     }
//     return options.filter(option =>
//       option.label.toLowerCase().includes(searchQuery.toLowerCase())
//     );
//   }, [options, searchQuery, searchable]);

//   const handleSelect = (option: DropdownOption) => {
//     onSelect(option);
//     setIsVisible(false);
//     setSearchQuery(''); // Reset search when closing
//   };

//   const handleModalClose = () => {
//     setIsVisible(false);
//     setSearchQuery(''); // Reset search when closing
//   };

//   const renderOption = ({ item }: { item: DropdownOption }) => (
//     <TouchableOpacity
//       style={[
//         styles.option,
//         item.id === selectedValue && styles.optionSelected,
//       ]}
//       onPress={() => handleSelect(item)}
//     >
//       <Text style={[
//         styles.optionText,
//         item.id === selectedValue && styles.optionTextSelected,
//       ]}>
//         {item.label}
//       </Text>
//       {item.id === selectedValue && (
//         <Check size={16} color="#B89C4C" />
//       )}
//     </TouchableOpacity>
//   );

//   return (
//     <View style={styles.container}>
//       {label && <Text style={styles.label}>{label}</Text>}
      
//       <TouchableOpacity
//         style={[
//           styles.selector,
//           error && styles.selectorError,
//         ]}
//         onPress={() => setIsVisible(true)}
//       >
//         <Text style={[
//           styles.selectorText,
//           !selectedOption && styles.placeholderText,
//         ]}>
//           {selectedOption ? selectedOption.label : placeholder}
//         </Text>
//         <ChevronDown size={20} color="#6B7280" />
//       </TouchableOpacity>

//       {error && <Text style={styles.errorText}>{error}</Text>}

//       <Modal
//         visible={isVisible}
//         transparent
//         animationType="fade"
//         onRequestClose={handleModalClose}
//       >
//         <TouchableOpacity
//           style={styles.overlay}
//           activeOpacity={1}
//           onPress={handleModalClose}
//         >
//           <View style={styles.modal}>
//             <View style={styles.modalHeader}>
//               <Text style={styles.modalTitle}>{label || 'Select Option'}</Text>
//               {searchable && (
//                 <View style={styles.searchContainer}>
//                   <Search size={16} color="#6B7280" style={styles.searchIcon} />
//                   <TextInput
//                     style={styles.searchInput}
//                     placeholder={searchPlaceholder}
//                     value={searchQuery}
//                     onChangeText={setSearchQuery}
//                     autoCapitalize="none"
//                     autoCorrect={false}
//                   />
//                 </View>
//               )}
//             </View>
//             <FlatList
//               data={filteredOptions}
//               renderItem={renderOption}
//               keyExtractor={(item) => item.id.toString()}
//               style={styles.optionsList}
//               showsVerticalScrollIndicator={false}
//               ListEmptyComponent={
//                 searchQuery.trim() ? (
//                   <View style={styles.emptyContainer}>
//                     <Text style={styles.emptyText}>No results found</Text>
//                   </View>
//                 ) : null
//               }
//             />
//           </View>
//         </TouchableOpacity>
//       </Modal>
//     </View>
//   );
// }

// const styles = StyleSheet.create({
//   container: {
//     marginBottom: 16,
//   },
//   label: {
//     fontSize: 14,
//     fontWeight: '500',
//     color: '#374151',
//     marginBottom: 8,
//   },
//   selector: {
//     backgroundColor: '#fff',
//     borderWidth: 1,
//     borderColor: '#D1D5DB',
//     borderRadius: 8,
//     padding: 12,
//     flexDirection: 'row',
//     alignItems: 'center',
//     justifyContent: 'space-between',
//   },
//   selectorError: {
//     borderColor: '#EF4444',
//   },
//   selectorText: {
//     fontSize: 16,
//     color: '#1F2937',
//     flex: 1,
//   },
//   placeholderText: {
//     color: '#9CA3AF',
//   },
//   errorText: {
//     fontSize: 12,
//     color: '#EF4444',
//     marginTop: 4,
//   },
//   overlay: {
//     flex: 1,
//     backgroundColor: 'rgba(0, 0, 0, 0.5)',
//     justifyContent: 'center',
//     alignItems: 'center',
//     padding: 20,
//   },
//   modal: {
//     backgroundColor: '#fff',
//     borderRadius: 12,
//     width: '100%',
//     maxHeight: '70%',
//     overflow: 'hidden',
//   },
//   modalHeader: {
//     padding: 16,
//     borderBottomWidth: 1,
//     borderBottomColor: '#E5E7EB',
//   },
//   modalTitle: {
//     fontSize: 18,
//     fontWeight: '600',
//     color: '#111827',
//     textAlign: 'center',
//     marginBottom: 12,
//   },
//   searchContainer: {
//     flexDirection: 'row',
//     alignItems: 'center',
//     backgroundColor: '#F9FAFB',
//     borderRadius: 8,
//     paddingHorizontal: 12,
//     paddingVertical: 8,
//     borderWidth: 1,
//     borderColor: '#E5E7EB',
//   },
//   searchIcon: {
//     marginRight: 8,
//   },
//   searchInput: {
//     flex: 1,
//     fontSize: 16,
//     color: '#1F2937',
//     padding: 0,
//   },
//   optionsList: {
//     maxHeight: 300,
//   },
//   option: {
//     padding: 16,
//     flexDirection: 'row',
//     alignItems: 'center',
//     justifyContent: 'space-between',
//     borderBottomWidth: 1,
//     borderBottomColor: '#F3F4F6',
//   },
//   optionSelected: {
//     backgroundColor: '#F9F9F9',
//   },
//   optionText: {
//     fontSize: 16,
//     color: '#1F2937',
//     flex: 1,
//   },
//   optionTextSelected: {
//     color: '#B89C4C',
//     fontWeight: '500',
//   },
//   emptyContainer: {
//     padding: 32,
//     alignItems: 'center',
//   },
//   emptyText: {
//     fontSize: 16,
//     color: '#6B7280',
//     textAlign: 'center',
//   },
// });
