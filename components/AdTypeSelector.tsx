import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Chrome as Home, DollarSign } from 'lucide-react-native';

type AdType = 'rent' | 'sale';

interface AdTypeSelectorProps {
  selectedType: AdType | null;
  onSelectType: (type: AdType) => void;
}

export default function AdTypeSelector({ selectedType, onSelectType }: AdTypeSelectorProps) {
  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={[
          styles.option,
          selectedType === 'rent' && styles.optionSelected,
        ]}
        onPress={() => {
          console.log('AdTypeSelector: Rent button pressed');
          onSelectType('rent');
        }}
      >
        <View style={[
          styles.iconContainer,
          selectedType === 'rent' && styles.iconContainerSelected,
        ]}>
          <Home
            size={24}
            color={selectedType === 'rent' ? '#B89C4C' : '#6B7280'}
          />
        </View>
        <Text style={[
          styles.optionTitle,
          selectedType === 'rent' && styles.optionTitleSelected,
        ]}>
          For Rent
        </Text>
        <Text style={styles.optionDescription}>
          Looking to rent a property
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={[
          styles.option,
          selectedType === 'sale' && styles.optionSelected,
        ]}
        onPress={() => onSelectType('sale')}
      >
        <View style={[
          styles.iconContainer,
          selectedType === 'sale' && styles.iconContainerSelected,
        ]}>
          <DollarSign
            size={24}
            color={selectedType === 'sale' ? '#B89C4C' : '#6B7280'}
          />
        </View>
        <Text style={[
          styles.optionTitle,
          selectedType === 'sale' && styles.optionTitleSelected,
        ]}>
          For Sale
        </Text>
        <Text style={styles.optionDescription}>
          Looking to buy a property
        </Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    gap: 16,
  },
  option: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  optionSelected: {
    borderColor: '#B89C4C',
    backgroundColor: '#FFFBEB',
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  iconContainerSelected: {
    backgroundColor: '#FEF3C7',
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 4,
  },
  optionTitleSelected: {
    color: '#B89C4C',
  },
  optionDescription: {
    fontSize: 14,
    color: '#6B7280',
  },
});