import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  FlatList,
  Dimensions,
  Modal,
} from 'react-native';
import Toast from 'react-native-toast-message';
import { useLocalSearchParams, Stack, router } from 'expo-router';
import { ArrowLeft } from 'lucide-react-native';
import { useQuery, useQueryClient, useMutation } from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';
import DateTimePicker from '@react-native-community/datetimepicker';
import {
  fetchLeadStatuses,
  fetchGeographies,
  fetchListings,
  fetchLead,
  api
} from '@/lib/api';

import ListingCard from '@/components/ListingCard';
import Pagination from '@/components/Pagination';
import ViewingScheduleForm from '@/components/ViewingScheduleForm';
import MeetingConfigForm from '@/components/MeetingConfigForm';
import FollowUpConfigForm from '@/components/FollowUpConfigForm';

import ViewingConfigForm from '@/components/ViewingConfigForm';
import PropertySearchModal from '@/components/PropertySearchModal';
import OfferNegotiationForm from '@/components/OfferNegotiationForm';
import SimpleStatusForm from '@/components/SimpleStatusForm';
import SelectedPropertiesPills from '@/components/SelectedPropertiesPills';

interface StatusOption {
  id: number;
  name: string;
  background_color: string;
  is_disabled: number;
}

interface MeetingConfig {
  title: string;
  content: string;
  dueDate: string;
  priority: string;
  sendEmail: boolean;
  remarks: string;
}

interface FollowUpConfig {
  title: string;
  content: string;
  dueDate: string;
  priority: string;
  sendEmail: boolean;
  remarks: string;
}



interface ViewingConfig {
  search: string;
  rentSale: string;
  towerBuilding: string;
  bedrooms: string[];
  minArea: string;
  maxArea: string;
  priceMin: string;
  priceMax: string;
  selectedProperties: string[];
  // Reminder fields
  reminderTitle: string;
  reminderContent: string;
  dueDate: string;
  priority: string;
  sendEmail: boolean;
  remarks: string;
}

const { height: screenHeight } = Dimensions.get('window');
const ITEMS_PER_PAGE = 10;

// Helper function to format date for API (Laravel expects YYYY-MM-DD HH:mm:ss format)
const formatDateForAPI = (dateString: string): string => {
  try {
    if (!dateString) return new Date().toISOString().slice(0, 19).replace('T', ' ');

    // Parse the date string (format: "DD/MM/YYYY, HH:mm")
    const [datePart, timePart] = dateString.split(', ');
    if (!datePart || !timePart) return new Date().toISOString().slice(0, 19).replace('T', ' ');

    const [day, month, year] = datePart.split('/');
    const [hour, minute] = timePart.split(':');

    // Create a Date object with full date and time
    const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day), parseInt(hour), parseInt(minute));

    // Format as YYYY-MM-DD HH:mm:ss for Laravel
    const formattedDate = date.getFullYear() + '-' +
                         String(date.getMonth() + 1).padStart(2, '0') + '-' +
                         String(date.getDate()).padStart(2, '0') + ' ' +
                         String(date.getHours()).padStart(2, '0') + ':' +
                         String(date.getMinutes()).padStart(2, '0') + ':00';

    console.log('🔍 Date formatting debug:');
    console.log('Input:', dateString);
    console.log('Parsed date object:', date);
    console.log('Formatted for API:', formattedDate);
    console.log('Current time:', new Date().toISOString());

    return formattedDate;
  } catch (error) {
    console.error('Error formatting date:', error);
    return new Date().toISOString().slice(0, 19).replace('T', ' '); // Fallback to now
  }
};

export default function EditStatusScreen() {
  const { leadId, currentStatus, isOfferNegotiation } = useLocalSearchParams();
  const queryClient = useQueryClient();
  const { user, session } = useAuth();
  const [selectedStatus, setSelectedStatus] = useState(currentStatus?.toString() || '');
  const [showMeetingConfig, setShowMeetingConfig] = useState(false);
  const [showViewingConfig, setShowViewingConfig] = useState(false);
  const [showFollowUpConfig, setShowFollowUpConfig] = useState(false);
  const [showOfferNegotiationConfig, setShowOfferNegotiationConfig] = useState(false);
  const [showSimpleStatusForm, setShowSimpleStatusForm] = useState(false);
  const [simpleStatusRemarks, setSimpleStatusRemarks] = useState('');
  const [currentViewingPage, setCurrentViewingPage] = useState(1);
  const [showViewingModal, setShowViewingModal] = useState(false);
  const [viewingModalStep, setViewingModalStep] = useState<'properties' | 'schedule'>('properties');
  const [showPropertySearchModal, setShowPropertySearchModal] = useState(false);

  const [showDatePicker, setShowDatePicker] = useState(false);
  const [currentConfigType, setCurrentConfigType] = useState<'meeting' | 'followUp' | 'viewing'>('viewing');

  const [meetingConfig, setMeetingConfig] = useState<MeetingConfig>({
    title: 'Meeting appointment',
    content: 'Meeting appointment reminder.',
    dueDate: '',
    priority: 'Low',
    sendEmail: false,
    remarks: '',
  });

  const [viewingConfig, setViewingConfig] = useState<ViewingConfig>({
    search: '',
    rentSale: '',
    towerBuilding: '',
    bedrooms: [],
    minArea: '',
    maxArea: '',
    priceMin: '',
    priceMax: '',
    selectedProperties: [],
    reminderTitle: 'Visit appointment',
    reminderContent: 'This is just a reminder that the following listings needs to be seen.',
    dueDate: '',
    priority: 'Low',
    sendEmail: false,
    remarks: '',
  });

  const [selectedProperties, setSelectedProperties] = useState<string[]>([]);
  const [selectedOfferProperties, setSelectedOfferProperties] = useState<string[]>([]);

  const [followUpConfig, setFollowUpConfig] = useState<FollowUpConfig>({
    title: 'Follow up',
    content: 'Follow up reminder.',
    dueDate: '',
    priority: 'Low',
    sendEmail: false,
    remarks: '',
  });



  const { data: leadStatuses = [] } = useQuery({
    queryKey: ['leadStatuses'],
    queryFn: fetchLeadStatuses,
  });

  useEffect(() => {
    if (isOfferNegotiation && leadStatuses.length > 0) {
      const offerStatus = leadStatuses.find((status: StatusOption) => status.name === 'OFFER_NEGOTIATION');
      if (offerStatus) {
        setSelectedStatus(offerStatus.id.toString());
        setShowOfferNegotiationConfig(true);
      }
    }
  }, [isOfferNegotiation, leadStatuses]);

  const { data: currentLead } = useQuery({
    queryKey: ['lead', leadId],
    queryFn: () => fetchLead(parseInt(leadId as string)),
    enabled: !!leadId,
  });

  useEffect(() => {
    if (currentLead) {
      console.log('📊 Current lead data:', currentLead);
      console.log('📊 Current lead status ID:', currentLead.lead_status_id);
      console.log('📊 Current lead status object:', (currentLead as any).leadStatus);
    }
  }, [currentLead]);

  const { data: geographies = [] } = useQuery({
    queryKey: ['geographies'],
    queryFn: fetchGeographies,
  });


  const transformApiDataToDropdownOptions = (data: any[]) => {
    if (!Array.isArray(data)) return [];

    return data.map((item) => ({
      id: item.id?.toString() || '',
      label: item.name || item.title || item.label || 'Unknown',
      value: item,
    }));
  };


  const selectedLocation = geographies.find((geo: any) => geo.id?.toString() === viewingConfig.search);

  const { data: towers = [{ id: 'any', name: 'Any' }] } = useQuery({
    queryKey: ['towers', selectedLocation?.id],
    queryFn: async () => {
      if (!selectedLocation?.id) {
        return [{ id: 'any', name: 'Any' }];
      }
      try {
        const { data } = await api.get(`/geography/${selectedLocation.id}/towers`);
        return [{ id: 'any', name: 'Any' }, ...data];
      } catch (error) {
        console.error('Error fetching towers:', error);
        return [{ id: 'any', name: 'Any' }];
      }
    },
    enabled: true,
  });

  const enabledStatuses = leadStatuses.filter((status: StatusOption) => status.is_disabled === 0);

  const createListingFilters = (config: ViewingConfig) => {
    const filters: any = {};

    if (config.search) {
      filters.location = config.search;
    }

    if (config.rentSale) {
      const rentSaleMap: any = {
        'rent': 'rent',
        'sale': 'sale',
        'rent_sale': 'All'
      };
      filters.adType = rentSaleMap[config.rentSale] || 'All';
    }

    return filters;
  };

  const hasFilters = viewingConfig?.search || viewingConfig?.rentSale;

  const hasUIFilters = viewingConfig?.towerBuilding ||
    (viewingConfig?.bedrooms && viewingConfig.bedrooms.length > 0) ||
    viewingConfig?.priceMin || viewingConfig?.priceMax;

  const viewingConfigForQuery = {
    search: viewingConfig.search,
    rentSale: viewingConfig.rentSale,
    towerBuilding: viewingConfig.towerBuilding,
    bedrooms: viewingConfig.bedrooms,
    minArea: viewingConfig.minArea,
    maxArea: viewingConfig.maxArea,
    priceMin: viewingConfig.priceMin,
    priceMax: viewingConfig.priceMax,
  };

  const { data: listingsData, isLoading: isLoadingListings } = useQuery({
    queryKey: ['modal-listings', viewingConfigForQuery, currentViewingPage],
    queryFn: () => {
      if (!hasFilters) {
        const params = {
          propertyType: undefined as number | undefined,
          adType: 'All' as string,
          vt: 'master' as string
        };
        return fetchListings(currentViewingPage, params);
      }
      const filters = createListingFilters(viewingConfig);
      const params = {
        ...filters,
        vt: 'master',
        adType: filters.adType || 'All'
      };
      return fetchListings(currentViewingPage, params);
    },
    enabled: showViewingConfig || showOfferNegotiationConfig, // Enable pentru ambele
  });

  const { data: totalCountData } = useQuery({
    queryKey: ['viewing-total-count', viewingConfigForQuery],
    queryFn: () => {
      if (!hasFilters) {
        const params = {
          propertyType: undefined as number | undefined,
          adType: 'All' as string,
          vt: 'master' as string
        };
        return fetchListings(1, params); 
      }
      const filters = createListingFilters(viewingConfig);
      const params = {
        ...filters,
        vt: 'master',
        adType: filters.adType || 'All'
      };
      return fetchListings(1, params); 
    },
    enabled: showViewingConfig || showOfferNegotiationConfig, // Enable pentru ambele
  });

  const applyClientSideFilters = (listings: any[]) => {
    if (!hasUIFilters) return listings;

    return listings.filter(listing => {
      if (viewingConfig.towerBuilding && viewingConfig.towerBuilding !== 'any') {
        if (listing.tower?.id?.toString() !== viewingConfig.towerBuilding) {
          return false;
        }
      }

      if (viewingConfig.bedrooms && viewingConfig.bedrooms.length > 0) {
        if (!viewingConfig.bedrooms.includes(listing.bedrooms_no?.toString())) {
          return false;
        }
      }

      if (viewingConfig.priceMin) {
        const minPrice = parseInt(viewingConfig.priceMin);
        const listingPrice = parseInt(listing.price?.replace(/[^0-9]/g, '') || '0');
        if (listingPrice < minPrice) {
          return false;
        }
      }

      if (viewingConfig.priceMax) {
        const maxPrice = parseInt(viewingConfig.priceMax);
        const listingPrice = parseInt(listing.price?.replace(/[^0-9]/g, '') || '0');
        if (listingPrice > maxPrice) {
          return false;
        }
      }

      return true;
    });
  };

  const rawListings = listingsData?.data || [];
  const listings = applyClientSideFilters(rawListings);
  const totalListings = listingsData?.total || 0;
  const totalViewingPages = Math.ceil(totalListings / ITEMS_PER_PAGE);

  const rawButtonListings = totalCountData?.data || [];
  const filteredButtonListings = applyClientSideFilters(rawButtonListings);
  const buttonTotalCount = hasUIFilters ? filteredButtonListings.length : (totalCountData?.total || 0);



  useEffect(() => {
    setSelectedStatus(currentStatus?.toString() || '');
  }, [currentStatus]);

  const formatStatusName = (name: string) => {
    return name.replace(/_/g, ' ');
  };

  const updateMeetingConfig = (field: keyof MeetingConfig, value: string | boolean) => {
    setMeetingConfig(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const updateViewingConfig = (field: keyof ViewingConfig, value: string | string[] | (string | number)[] | boolean) => {
    setViewingConfig(prev => ({
      ...prev,
      [field]: value
    }));

    if (field === 'selectedProperties' && Array.isArray(value)) {
      setSelectedProperties(value as string[]);
    }
  };

  const updateFollowUpConfig = (field: keyof FollowUpConfig, value: string | boolean) => {
    setFollowUpConfig(prev => ({
      ...prev,
      [field]: value
    }));
  };



  const handleDatePress = (configType: 'meeting' | 'followUp' | 'viewing') => {
    console.log('Date picker pressed for:', configType);
    setCurrentConfigType(configType);
    setShowDatePicker(true);
  };

  const handleDateChange = (_event: any, selectedDate?: Date) => {
    setShowDatePicker(false);

    if (selectedDate) {
      const formattedDate = selectedDate.toLocaleDateString('en-GB', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
      }) + ', ' + selectedDate.toLocaleTimeString('en-GB', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false,
      });

      if (currentConfigType === 'meeting') {
        updateMeetingConfig('dueDate', formattedDate);
      } else if (currentConfigType === 'followUp') {
        updateFollowUpConfig('dueDate', formattedDate);
      } else if (currentConfigType === 'viewing') {
        updateViewingConfig('dueDate', formattedDate);
      }
    }
  };

  const handleStatusToggle = (statusId: string) => {
    setSelectedStatus(statusId);

    setShowMeetingConfig(false);
    setShowViewingConfig(false);
    setShowFollowUpConfig(false);
    setShowOfferNegotiationConfig(false);
    setShowSimpleStatusForm(false);
    setSimpleStatusRemarks('');

    resetViewingFilters();
    setSelectedProperties([]);
    setShowViewingModal(false);
    setViewingModalStep('properties');

    const selectedStatusObj = leadStatuses.find((s: StatusOption) => s.id.toString() === statusId);
    if (selectedStatusObj) {
      if (selectedStatusObj.name === 'MEETING_SCHEDULED') {
        setShowMeetingConfig(true);
      } else if (selectedStatusObj.name === 'VIEWING_SCHEDULED') {
        setCurrentViewingPage(1);
        setShowViewingConfig(true);
      } else if (selectedStatusObj.name === 'FOLLOW_UP') {
        setShowFollowUpConfig(true);
      } else if (selectedStatusObj.name === 'OFFER_NEGOTIATION') {
        setShowOfferNegotiationConfig(true);
      } else {
        setShowSimpleStatusForm(true);
      }
    }
  };

  const validateRemarks = (statusId: string, remarks: string): string | null => {
    const statusesThatDontRequireRemarks = ['14', '15', '23', '26'];
    const isRemarksRequired = !statusesThatDontRequireRemarks.includes(statusId);

    if (isRemarksRequired && (!remarks || remarks.trim() === '')) {
      return 'Remarks are required for this status';
    }

    return null;
  };

  const getStatusSpecificMessages = (statusId: string) => {
    const statusObj = leadStatuses.find((s: StatusOption) => s.id.toString() === statusId);
    const statusName = statusObj?.name || 'Unknown';

    switch (statusName) {
      case 'MEETING_SCHEDULED':
        return {
          success: {
            title: 'Meeting Scheduled',
            message: 'Meeting has been successfully scheduled with the client.'
          },
          validation: {
            title: 'Meeting Details Required',
            message: 'Please provide date, time, and remarks for the meeting.'
          }
        };
      case 'VIEWING_SCHEDULED':
        return {
          success: {
            title: 'Viewing Scheduled',
            message: 'Property viewing has been successfully scheduled.'
          },
          validation: {
            title: 'Viewing Details Required',
            message: 'Please select properties and provide viewing details.'
          }
        };
      case 'FOLLOW_UP':
        return {
          success: {
            title: 'Follow-up Scheduled',
            message: 'Follow-up reminder has been successfully created.'
          },
          validation: {
            title: 'Follow-up Details Required',
            message: 'Please provide date, time, and follow-up details.'
          }
        };
      case 'OFFER_NEGOTIATION':
        return {
          success: {
            title: 'Offer Negotiation Started',
            message: 'Offer negotiation process has been initiated successfully.'
          },
          validation: {
            title: 'Offer Details Required',
            message: 'Please select properties and provide negotiation remarks.'
          }
        };
      default:
        return {
          success: {
            title: 'Status Updated',
            message: `Lead status has been changed to ${statusName.replace(/_/g, ' ').toLowerCase()}.`
          },
          validation: {
            title: 'Status Change Error',
            message: 'Please provide the required information for this status.'
          }
        };
    }
  };

  const formatErrorMessage = (error: any, statusId?: string): { title: string; message: string } => {
    if (error.response?.status === 422) {
      const errors = error.response.data?.errors;
      if (errors && typeof errors === 'object') {
        const firstField = Object.keys(errors)[0];
        const firstError = errors[firstField]?.[0];
        return {
          title: 'Validation Error',
          message: firstError || 'Please check your input and try again.'
        };
      }
      return {
        title: 'Validation Error',
        message: error.response.data?.message || 'Please check your input and try again.'
      };
    } else if (error.response?.status === 400) {
      return {
        title: 'Invalid Request',
        message: error.response.data?.message || 'The request could not be processed.'
      };
    } else if (error.response?.status === 500) {
      return {
        title: 'Server Error',
        message: 'Something went wrong on our end. Please try again later.'
      };
    } else if (error.message) {
      if (statusId) {
        const statusMessages = getStatusSpecificMessages(statusId);
        return {
          title: statusMessages.validation.title,
          message: error.message
        };
      }
      return {
        title: 'Error',
        message: error.message
      };
    } else {
      return {
        title: 'Network Error',
        message: 'Please check your internet connection and try again.'
      };
    }
  };

  const updateStatusMutation = useMutation({
    mutationFn: async ({ statusId, config }: { statusId: string, config?: any }) => {
      const selectedStatusObj = leadStatuses.find((s: StatusOption) => s.id.toString() === statusId);

      const payload: any = {
        status: parseInt(statusId),
        agent: user?.id,
      };

      console.log('=== STATUS CHANGE DEBUG INFO ===');
      console.log('Lead ID:', leadId);
      console.log('Status ID:', statusId);
      console.log('Selected Status Object:', selectedStatusObj);
      console.log('Config received:', config);
      console.log('Available Lead Statuses:', leadStatuses.map(s => ({ id: s.id, name: s.name })));
      console.log('Is status valid?', leadStatuses.some(s => s.id.toString() === statusId));

      if (selectedStatusObj) {
        if (selectedStatusObj.name === 'MEETING_SCHEDULED' && config) {
          payload.viewingScheduledData = {
            reminder: {
              title: config.title || 'Meeting appointment',
              content: config.content || 'Meeting appointment reminder.',
              priority: config.priority?.toLowerCase() || 'low',
              dueDate: config.dueDate ? formatDateForAPI(config.dueDate) : undefined,
              sendEmail: config.sendEmail || false,
              sendReminderDate: config.dueDate ? formatDateForAPI(config.dueDate) : undefined,
            },
            listingSelection: {},
            remarks: config.remarks || '',
          };
          payload.remarks = config.remarks || 'Meeting scheduled';
        }
        else if (selectedStatusObj.name === 'VIEWING_SCHEDULED' && config) {
          console.log('🔍 VIEWING_SCHEDULED Debug:');
          console.log('🔍 Config received:', config);
          console.log('🔍 Selected properties:', config.selectedProperties);
          console.log('🔍 Due date:', config.dueDate);
          console.log('🔍 Remarks:', config.remarks);

          if (config.selectedProperties && config.selectedProperties.length > 0) {
            payload.listing_ids = config.selectedProperties.map((propertyId: string) => parseInt(propertyId));
            console.log('🔍 Added listing_ids to payload:', payload.listing_ids);
          } else {
            console.log('🔍 No selected properties found!');
          }

          let listingSelection: { [key: string]: string } = {};
          if (config.selectedProperties && config.selectedProperties.length > 0) {
            config.selectedProperties.forEach((propertyId: string) => {
              const property = listings.find(listing => listing.id.toString() === propertyId);
              if (property) {
                listingSelection[propertyId] = property.ref_no;
              }
            });
          }

          payload.viewingScheduledData = {
            reminder: {
              title: config.reminderTitle || 'Viewing appointment',
              content: config.reminderContent || 'Viewing appointment reminder.',
              priority: config.priority?.toLowerCase() || 'low',
              dueDate: config.dueDate ? formatDateForAPI(config.dueDate) : undefined,
              sendEmail: config.sendEmail || false,
              sendReminderDate: config.dueDate ? formatDateForAPI(config.dueDate) : undefined,
            },
            listingSelection: listingSelection,
            remarks: config.remarks || '',
          };
          payload.remarks = config.remarks || 'Viewing scheduled';
        }
        else if (selectedStatusObj.name === 'FOLLOW_UP' && config) {
          payload.viewingScheduledData = {
            reminder: {
              title: config.title || 'Follow up',
              content: config.content || 'Follow up reminder.',
              priority: config.priority?.toLowerCase() || 'low',
              dueDate: config.dueDate ? formatDateForAPI(config.dueDate) : undefined,
              sendEmail: config.sendEmail || false,
              sendReminderDate: config.dueDate ? formatDateForAPI(config.dueDate) : undefined,
            },
            listingSelection: {},
            remarks: config.remarks || '',
          };
          payload.remarks = config.remarks || 'Follow up scheduled';
        }
        else if (selectedStatusObj.name === 'OFFER_NEGOTIATION' && config) {
          if (!config.remarks || config.remarks.trim() === '') {
            throw new Error('Remarks are required for Offer Negotiation');
          }

          let listingSelection: { [key: string]: string } = {};
          if (config.selectedProperties && config.selectedProperties.length > 0) {
            config.selectedProperties.forEach((propertyId: string) => {
              const property = listings.find(listing => listing.id.toString() === propertyId);
              if (property) {
                listingSelection[propertyId] = property.ref_no;
              }
            });
          }

          payload.viewingScheduledData = {
            reminder: {
              title: 'Offer Negotiation',
              content: config.remarks,
              priority: 'medium',
              dueDate: formatDateForAPI(''),
              sendEmail: false,
              sendReminderDate: formatDateForAPI(''),
            },
            listingSelection: listingSelection,
            remarks: config.remarks,
          };
          payload.remarks = config.remarks;
        }
        else {
          payload.remarks = config?.remarks || `Status changed to ${selectedStatusObj.name.replace(/_/g, ' ').toLowerCase()}`;
        }
      }

      console.log('=== FINAL PAYLOAD DEBUG ===');
      console.log('Status ID:', statusId);
      console.log('Selected status object:', selectedStatusObj);
      console.log('Final payload to send:', JSON.stringify(payload, null, 2));
      console.log('=== END DEBUG INFO ===');

      const response = await api.patch(`/v2/leads/${leadId}/statusChange`, payload);
      return response.data;
    },
    onMutate: async ({ statusId, config }) => {
      console.log('🔄 Starting optimistic update...');

      await queryClient.cancelQueries({ queryKey: ['lead', leadId] });
      await queryClient.cancelQueries({ queryKey: ['lead', parseInt(leadId as string)] });
      await queryClient.cancelQueries({ queryKey: ['leads'] });

      const previousLead = queryClient.getQueryData(['lead', leadId]);
      const previousLeadInt = queryClient.getQueryData(['lead', parseInt(leadId as string)]);
      const previousLeads = queryClient.getQueryData(['leads']);

      const selectedStatusObj = leadStatuses.find((s: StatusOption) => s.id.toString() === statusId);
      console.log('🔄 Optimistic update - Selected status:', selectedStatusObj);

      if (previousLead) {
        const updateFunction = (old: any) => {
          if (!old) return old;
          const updated = {
            ...old,
            lead_status_id: parseInt(statusId),
            leadStatus: selectedStatusObj,
            status: selectedStatusObj?.name || old.status
          };
          console.log('🔄 Optimistic update - Updated lead:', updated);
          return updated;
        };

        queryClient.setQueryData(['lead', leadId], updateFunction);
        queryClient.setQueryData(['lead', parseInt(leadId as string)], updateFunction);
      }

      const statusMessages = getStatusSpecificMessages(statusId);
      Toast.show({
        type: 'success',
        text1: statusMessages.success.title,
        text2: statusMessages.success.message,
        position: 'top',
        visibilityTime: 3000,
        autoHide: true,
      });

      console.log('🔄 Optimistic update complete, navigating back...');
      router.back();

      return { previousLead, previousLeadInt, previousLeads };
    },
    onError: (error: any, variables, context) => {
      if (context?.previousLead) {
        queryClient.setQueryData(['lead', leadId], context.previousLead);
      }
      if (context?.previousLeadInt) {
        queryClient.setQueryData(['lead', parseInt(leadId as string)], context.previousLeadInt);
      }
      if (context?.previousLeads) {
        queryClient.setQueryData(['leads'], context.previousLeads);
      }

      const { statusId } = variables;
      const { title, message } = formatErrorMessage(error, statusId);

      Toast.show({
        type: 'error',
        text1: title,
        text2: message,
        position: 'top',
        visibilityTime: Math.max(5000, message.length * 50), 
        autoHide: true,
      });

      console.error('=== STATUS CHANGE ERROR ===');
      console.error('Status ID:', statusId);
      console.error('Error:', error.message);
      console.error('HTTP Status:', error.response?.status);
      console.error('Response:', error.response?.data);
      console.error('=== END ERROR ===');
    },
    onSuccess: (data, variables) => {
      console.log('✅ Status change successful, confirming cache update...');
      console.log('✅ Response data:', data);

      if (data && data.lead) {
        console.log('✅ Updating cache with server response...');
        queryClient.setQueryData(['lead', leadId], data);
        queryClient.setQueryData(['lead', parseInt(leadId as string)], data);
      }

      queryClient.invalidateQueries({ queryKey: ['leads'] });
      queryClient.invalidateQueries({ queryKey: ['tasks'] });

      console.log('✅ Cache confirmed and related queries invalidated');
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ['lead', leadId] });
      queryClient.invalidateQueries({ queryKey: ['leads'] });
    },
  });

  const parseDateFromFormat = (dateString: string): Date | null => {
    if (!dateString) return null;

    try {
      const [datePart, timePart] = dateString.split(', ');
      const [day, month, year] = datePart.split('/').map(Number);
      const [hour, minute] = timePart.split(':').map(Number);

      const date = new Date(year, month - 1, day, hour, minute);

      if (isNaN(date.getTime())) {
        return null;
      }

      return date;
    } catch (error) {
      console.error('Error parsing date:', dateString, error);
      return null;
    }
  };

  const validateStatusRequirements = (statusId: string): string | null => {
    const statusObj = leadStatuses.find((s: StatusOption) => s.id.toString() === statusId);
    const statusName = statusObj?.name || 'Unknown';

    switch (statusName) {
      case 'MEETING_SCHEDULED':
        if (showMeetingConfig) {
          if (!meetingConfig.dueDate) {
            return 'Due date is required for Meeting Scheduled';
          }
          // Validate date is not in the past
          const meetingDate = parseDateFromFormat(meetingConfig.dueDate);
          if (!meetingDate) {
            return 'Invalid meeting date format';
          }
          const now = new Date();
          if (meetingDate < now) {
            return 'Meeting date and time cannot be in the past';
          }
          if (!meetingConfig.remarks || meetingConfig.remarks.trim() === '') {
            return 'Remarks are required for Meeting Scheduled';
          }
        }
        break;

      case 'VIEWING_SCHEDULED':
        if (showViewingConfig) {
          if (!selectedProperties || selectedProperties.length === 0) {
            return 'At least one property must be selected for Viewing Scheduled';
          }
          if (!viewingConfig.dueDate) {
            return 'Due date is required for Viewing Scheduled';
          }
          // Validate date is not in the past
          const viewingDate = parseDateFromFormat(viewingConfig.dueDate);
          if (!viewingDate) {
            return 'Invalid viewing date format';
          }
          const now = new Date();
          if (viewingDate < now) {
            return 'Viewing date and time cannot be in the past';
          }
          if (!viewingConfig.remarks || viewingConfig.remarks.trim() === '') {
            return 'Remarks are required for Viewing Scheduled';
          }
        }
        break;

      case 'FOLLOW_UP':
        if (showFollowUpConfig) {
          if (!followUpConfig.dueDate) {
            return 'Due date is required for Follow Up';
          }
          // Validate date is not in the past
          const followUpDate = parseDateFromFormat(followUpConfig.dueDate);
          if (!followUpDate) {
            return 'Invalid follow-up date format';
          }
          const now = new Date();
          if (followUpDate < now) {
            return 'Follow-up date and time cannot be in the past';
          }
          if (!followUpConfig.remarks || followUpConfig.remarks.trim() === '') {
            return 'Remarks are required for Follow Up';
          }
        }
        break;

      case 'OFFER_NEGOTIATION':
        if (showOfferNegotiationConfig) {
          if (!selectedOfferProperties || selectedOfferProperties.length === 0) {
            return 'At least one property must be selected for Offer Negotiation';
          }
          if (!simpleStatusRemarks || simpleStatusRemarks.trim() === '') {
            return 'Remarks are required for Offer Negotiation';
          }
        }
        break;

      default:
        // For simple status form, validate remarks
        if (showSimpleStatusForm && !isOfferNegotiation) {
          return validateRemarks(statusId, simpleStatusRemarks);
        }
        break;
    }

    return null;
  };

  const handleSaveStatus = (statusId: string, config?: any) => {
    console.log('Saving status:', statusId, 'with config:', config);

    // Validate status-specific requirements
    const validationError = validateStatusRequirements(statusId);
    if (validationError) {
      const statusMessages = getStatusSpecificMessages(statusId);

      Toast.show({
        type: 'error',
        text1: statusMessages.validation.title,
        text2: validationError,
        position: 'top',
        visibilityTime: Math.max(4000, validationError.length * 50), // Timp dinamic bazat pe lungimea mesajului
        autoHide: true,
      });
      return; // Stop execution if validation fails
    }

    updateStatusMutation.mutate({ statusId, config });
  };

  const toggleListingSelection = useCallback((listingId: string) => {
    setSelectedProperties(prev =>
      prev.includes(listingId)
        ? prev.filter(id => id !== listingId)
        : [...prev, listingId]
    );
  }, []);

  const renderListingItem = useCallback(({ item }: { item: any }) => (
    <TouchableOpacity
      style={styles.listingItem}
      onPress={() => toggleListingSelection(item.id.toString())}
      activeOpacity={0.7}
    >
      <View style={styles.listingCheckboxContainer}>
        <View
          style={[
            styles.listingCheckbox,
            selectedProperties.includes(item.id.toString()) && styles.listingCheckboxSelected
          ]}
        >
          {selectedProperties.includes(item.id.toString()) && (
            <Text style={styles.listingCheckmark}>✓</Text>
          )}
        </View>
      </View>
      <View style={styles.listingCardContainer}>
        <ListingCard
          listing={item}
          disableNavigation={true}
          compact={true}
        />
      </View>
    </TouchableOpacity>
  ), [selectedProperties, toggleListingSelection]);

  const getItemLayout = useCallback((_: any, index: number) => ({
    length: 90,
    offset: 90 * index,
    index,
  }), []);

  const resetViewingFilters = () => {
    setViewingConfig({
      search: '',
      rentSale: '',
      towerBuilding: '',
      bedrooms: [],
      minArea: '',
      maxArea: '',
      priceMin: '',
      priceMax: '',
      selectedProperties: [],
      reminderTitle: 'Visit appointment',
      reminderContent: 'This is just a reminder that the following listings needs to be seen.',
      dueDate: '',
      priority: 'Low',
      sendEmail: false,
      remarks: '',
    });
    setSelectedProperties([]);
    setCurrentViewingPage(1);
    queryClient.removeQueries({ queryKey: ['modal-listings'] });
  };

  const handleViewingPageChange = (page: number) => {
    setCurrentViewingPage(page);
  };

  const handleOpenViewingModal = () => {
    setCurrentViewingPage(1); 
    setShowViewingModal(true);
    setViewingModalStep('properties');
  };

  const handleCloseViewingModal = () => {
    setShowViewingModal(false);
    setViewingModalStep('properties');
    setSelectedProperties([]);
  };

  const handleScheduleViewing = () => {
    setViewingModalStep('schedule');
  };

  const handleScheduleViewingSave = () => {
    const configWithSelectedProperties = {
      ...viewingConfig,
      selectedProperties: selectedProperties
    };
    handleSaveStatus(selectedStatus, configWithSelectedProperties);
    handleCloseViewingModal();
  };



  const handleBack = () => {
    router.back();
  };

  const handleOpenPropertySearchModal = () => {
    setShowPropertySearchModal(true);
  };

  const handleClosePropertySearchModal = () => {
    setShowPropertySearchModal(false);
  };

  const handleSelectPropertiesFromSearch = (selectedPropertyIds: string[]) => {
    if (showOfferNegotiationConfig) {
      // Pentru Offer Negotiation
      setSelectedOfferProperties(selectedPropertyIds);
    } else {
      // Pentru Viewing Scheduled
      setSelectedProperties(selectedPropertyIds);
      updateViewingConfig('selectedProperties', selectedPropertyIds);
    }
    setShowPropertySearchModal(false);
  };

  // Offer negotiation functions
  const handleOfferPropertiesChange = (properties: string[]) => {
    setSelectedOfferProperties(properties);
  };



  return (
    <>
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />

      <SafeAreaView style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={handleBack} style={styles.backButton}>
            <ArrowLeft size={24} color="#000" />
          </TouchableOpacity>
          <Text style={styles.title}>Select Status</Text>
          <View style={styles.placeholder} />
        </View>

        {/* Status Pills */}
        <View style={styles.statusContainer}>
          <ScrollView
            horizontal={true}
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.statusPillsWrapper}
          >
            {enabledStatuses.map((status: StatusOption) => (
              <TouchableOpacity
                key={status.id}
                style={[
                  styles.statusPill,
                  selectedStatus === status.id.toString() && styles.selectedStatusPill
                ]}
                onPress={() => handleStatusToggle(status.id.toString())}
              >
                <Text
                  style={[
                    styles.statusPillText,
                    selectedStatus === status.id.toString() && styles.selectedStatusPillText
                  ]}
                >
                  {formatStatusName(status.name)}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        <View style={styles.content}>


          {showMeetingConfig && (
            <MeetingConfigForm
              meetingConfig={meetingConfig}
              updateMeetingConfig={updateMeetingConfig}
              handleDatePress={handleDatePress}
            />
          )}

          {showFollowUpConfig && (
            <FollowUpConfigForm
              followUpConfig={followUpConfig}
              updateFollowUpConfig={updateFollowUpConfig}
              handleDatePress={handleDatePress}
            />
          )}



          {showViewingConfig && (
            <ViewingConfigForm
              viewingConfig={viewingConfig}
              updateViewingConfig={updateViewingConfig}
              selectedProperties={selectedProperties}
              listings={listings}
              onSearchProperties={handleOpenPropertySearchModal}
            />
          )}

          {/* Offer Negotiation Configuration */}
          {showOfferNegotiationConfig && (
            <View style={styles.offerNegotiationContainer}>
              {/* Search Properties Button */}
              <View style={styles.section}>
                <TouchableOpacity
                  style={styles.searchPropertiesButton}
                  onPress={handleOpenPropertySearchModal}
                >
                  <Text style={styles.searchPropertiesButtonText}>Search Properties</Text>
                </TouchableOpacity>
              </View>

              {/* Selected Properties Display */}
              <SelectedPropertiesPills
                selectedProperties={selectedOfferProperties}
                listings={listings}
                onRemoveProperty={(propertyId) => {
                  const newSelectedProperties = selectedOfferProperties.filter(id => id !== propertyId);
                  setSelectedOfferProperties(newSelectedProperties);
                }}
                showNavigationOnPress={true}
                title="Selected Properties"
              />

              {/* SimpleStatusForm pentru remarks */}
              <SimpleStatusForm
                statusName={leadStatuses.find((s: StatusOption) => s.id.toString() === selectedStatus)?.name || ''}
                onRemarksChange={setSimpleStatusRemarks}
              />
            </View>
          )}

          {showSimpleStatusForm && !isOfferNegotiation && (
            <SimpleStatusForm
              statusName={leadStatuses.find((s: StatusOption) => s.id.toString() === selectedStatus)?.name || ''}
              onRemarksChange={setSimpleStatusRemarks}
            />
          )}



          {!showMeetingConfig && !showViewingConfig && !showFollowUpConfig && !showOfferNegotiationConfig && !showSimpleStatusForm && (
            <View style={styles.instructionContainer}>
              <Text style={styles.instructionText}>
                Select a status above. Some statuses will show additional configuration options.
              </Text>
            </View>
          )}
        </View>



        {selectedStatus && (
          <View style={styles.saveButtonContainer}>
            <TouchableOpacity
              style={[
                styles.saveButton,
                updateStatusMutation.isPending && styles.saveButtonDisabled
              ]}
              onPress={() => {
                let config: any = undefined;

                if (showMeetingConfig) {
                  config = meetingConfig;
                } else if (showFollowUpConfig) {
                  config = followUpConfig;
                } else if (showViewingConfig) {
                  config = {
                    ...viewingConfig,
                    selectedProperties: selectedProperties
                  };
                } else if (showOfferNegotiationConfig) {
                  config = {
                    selectedProperties: selectedOfferProperties,
                    remarks: simpleStatusRemarks
                  };
                } else if (showSimpleStatusForm) {
                  const selectedStatusObj = leadStatuses.find((s: StatusOption) => s.id.toString() === selectedStatus);
                  config = {
                    remarks: simpleStatusRemarks || `Status changed to ${selectedStatusObj?.name.replace(/_/g, ' ').toLowerCase() || 'new status'}`
                  };
                }

                handleSaveStatus(selectedStatus, config);
              }}
              disabled={updateStatusMutation.isPending}
            >
              <Text style={styles.saveButtonText}>
                {updateStatusMutation.isPending
                  ? 'Saving...'
                  : showOfferNegotiationConfig
                    ? `Save Status (${selectedOfferProperties.length} properties)`
                    : showViewingConfig && selectedProperties.length > 0
                      ? `Save Status (${selectedProperties.length} properties)`
                      : 'Save Status'
                }
              </Text>
            </TouchableOpacity>
          </View>
        )}
      </SafeAreaView>

      {/* Viewing Modal with 2 Steps */}
      <Modal
        visible={showViewingModal}
        transparent={true}
        animationType="slide"
        onRequestClose={handleCloseViewingModal}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            {/* Header */}
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>
                {viewingModalStep === 'properties' ? 'Select Properties' : 'Schedule Viewing'}
              </Text>
              <TouchableOpacity onPress={handleCloseViewingModal} style={styles.modalCloseButton}>
                <Text style={styles.modalCloseText}>✕</Text>
              </TouchableOpacity>
            </View>

            {/* Step 1: Properties List */}
            {viewingModalStep === 'properties' && (
              <View style={styles.modalContent}>
                {isLoadingListings ? (
                  <View style={styles.loadingContainer}>
                    <Text style={styles.loadingText}>Loading properties...</Text>
                  </View>
                ) : listings.length > 0 ? (
                  <>
                    <Text style={styles.propertiesCount}>
                      {listings.length} of {totalListings} properties
                    </Text>
                    <FlatList
                      data={listings}
                      keyExtractor={(item) => item.id.toString()}
                      renderItem={renderListingItem}
                      getItemLayout={getItemLayout}
                      style={styles.modalListingsList}
                      showsVerticalScrollIndicator={false}
                      removeClippedSubviews={true}
                      maxToRenderPerBatch={10}
                      windowSize={10}
                      ListFooterComponent={() =>
                        totalViewingPages > 1 ? (
                          <View style={styles.modalPaginationContainer}>
                            <Pagination
                              currentPage={currentViewingPage}
                              totalPages={totalViewingPages}
                              onPageChange={handleViewingPageChange}
                            />
                          </View>
                        ) : null
                      }
                    />
                    <View style={styles.modalButtonContainer}>
                      <TouchableOpacity
                        style={[
                          styles.scheduleButton,
                          selectedProperties.length === 0 && styles.scheduleButtonDisabled
                        ]}
                        disabled={selectedProperties.length === 0}
                        onPress={handleScheduleViewing}
                      >
                        <Text style={[
                          styles.scheduleButtonText,
                          selectedProperties.length === 0 && styles.scheduleButtonTextDisabled
                        ]}>
                          Schedule viewing ({selectedProperties.length})
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </>
                ) : (
                  <View style={styles.noPropertiesContainer}>
                    <Text style={styles.noPropertiesText}>No properties found</Text>
                  </View>
                )}
              </View>
            )}

            {/* Step 2: Schedule Form */}
            {viewingModalStep === 'schedule' && (
              <ViewingScheduleForm
                selectedProperties={selectedProperties}
                listings={listings}
                viewingConfig={viewingConfig}
                updateViewingConfig={updateViewingConfig}
                handleDatePress={handleDatePress}
                onBack={() => setViewingModalStep('properties')}
                onConfirm={handleScheduleViewingSave}
              />
            )}
          </View>
        </View>
      </Modal>

      {/* Property Search Modal */}
      <PropertySearchModal
        visible={showPropertySearchModal}
        onClose={handleClosePropertySearchModal}
        onSelectProperties={handleSelectPropertiesFromSearch}
        initialSelectedProperties={showOfferNegotiationConfig ? selectedOfferProperties : selectedProperties}
      />
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    padding: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
  },
  placeholder: {
    width: 40,
  },
  statusContainer: {
    padding: 16,
  },
  statusPillsWrapper: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  statusPill: {
    paddingHorizontal: 15,
    paddingVertical: 7,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#B89C4C',
    backgroundColor: 'transparent',
  },
  selectedStatusPill: {
    backgroundColor: '#B89C4C',
  },
  statusPillText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#B89C4C',
    textTransform: 'capitalize',
  },
  selectedStatusPillText: {
    color: '#fff',
  },
  content: {
    flex: 1,
  },
  instructionContainer: {
    flex: 1,
    padding: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  instructionText: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 24,
  },
  saveButtonContainer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    backgroundColor: '#fff',
  },
  saveButton: {
    backgroundColor: '#B89C4C',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: 'center',
  },
  saveButtonDisabled: {
    backgroundColor: '#9CA3AF',
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },

  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 8,
  },
  textInput: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    color: '#111827',
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  dateInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
  },
  dateInput: {
    flex: 1,
    paddingRight: 40,
  },
  calendarButton: {
    position: 'absolute',
    right: 8,
    top: 10,
    padding: 4,
  },
  inlineDatePicker: {
    marginTop: 8,
  },
  errorText: {
    fontSize: 12,
    color: '#EF4444',
    marginTop: 4,
  },
  dropdownButton: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dropdownText: {
    fontSize: 16,
    color: '#111827',
  },
  dropdownArrow: {
    fontSize: 12,
    color: '#6B7280',
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  checkboxLabel: {
    fontSize: 16,
    color: '#111827',
    marginLeft: 12,
  },

  // Viewing Configuration Styles
  fullWidthContainer: {
    marginBottom: 16,
  },
  pillScrollContainer: {
    flexDirection: 'row',
  },
  pillScrollContent: {
    paddingRight: 16,
  },
  filterPill: {
    paddingHorizontal: 15,
    paddingVertical: 7,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#B89C4C',
    backgroundColor: 'transparent',
    marginRight: 8,
  },
  selectedFilterPill: {
    backgroundColor: '#B89C4C',
  },
  filterPillText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#B89C4C',
  },
  selectedFilterPillText: {
    color: '#fff',
  },
  viewingButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 16,
    gap: 12,
  },
  resetButton: {
    flex: 1,
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  resetButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
  },
  propertiesButton: {
    flex: 2,
    backgroundColor: '#B89C4C',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  propertiesButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#fff',
  },
  // Modal Styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContainer: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: screenHeight * 0.9,
    minHeight: screenHeight * 0.7,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
  },
  modalCloseButton: {
    padding: 4,
  },
  modalCloseText: {
    fontSize: 18,
    color: '#6B7280',
  },
  modalContent: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#6B7280',
  },
  propertiesCount: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 12,
  },
  modalListingsList: {
    flex: 1,
  },
  modalPaginationContainer: {
    marginTop: 16,
    alignItems: 'center',
  },
  modalButtonContainer: {
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    flexDirection: 'row',
    gap: 12,
  },
  scheduleButton: {
    flex: 1,
    backgroundColor: '#B89C4C',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  scheduleButtonDisabled: {
    backgroundColor: '#E5E7EB',
  },
  scheduleButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#fff',
  },
  scheduleButtonTextDisabled: {
    color: '#9CA3AF',
  },
  noPropertiesContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  noPropertiesText: {
    fontSize: 16,
    color: '#6B7280',
  },


  dateButton: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    marginTop: 8,
  },
  dateButtonText: {
    fontSize: 14,
    color: '#6B7280',
  },
  priorityContainer: {
    flexDirection: 'row',
    marginTop: 8,
    gap: 8,
  },
  priorityButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    backgroundColor: '#fff',
  },
  priorityButtonActive: {
    backgroundColor: '#B89C4C',
    borderColor: '#B89C4C',
  },
  priorityButtonText: {
    fontSize: 14,
    color: '#6B7280',
  },
  priorityButtonTextActive: {
    color: '#fff',
  },
  switchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginVertical: 16,
  },
  switchLabel: {
    fontSize: 14,
    color: '#374151',
    fontWeight: '500',
  },

  // Listing Item Styles (for modal)
  listingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    padding: 8,
  },
  listingCheckboxContainer: {
    marginRight: 12,
  },
  listingCheckbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: '#D1D5DB',
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
  },
  listingCheckboxSelected: {
    backgroundColor: '#B89C4C',
    borderColor: '#B89C4C',
  },
  listingCheckmark: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  listingCardContainer: {
    flex: 1,
  },

  // Config container styles
  configContainer: {
    padding: 16,
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    margin: 16,
  },
  configTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 8,
  },
  configDescription: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 16,
    lineHeight: 20,
  },
  openModalButton: {
    backgroundColor: '#B89C4C',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    alignItems: 'center',
  },
  openModalButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
  },

  // Properties list styles
  propertiesListContainer: {
    flex: 1,
    marginTop: 16,
  },
  propertiesListTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 12,
  },

  // Summary styles
  summaryContainer: {
    flex: 1,
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 12,
  },
  summaryPropertyItem: {
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
    borderLeftWidth: 3,
    borderLeftColor: '#B89C4C',
  },
  summaryPropertyText: {
    fontSize: 14,
    color: '#374151',
    fontWeight: '500',
  },

  // Button styles
  secondaryButton: {
    flex: 1,
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
    marginRight: 8,
  },
  secondaryButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
  },
  primaryButton: {
    flex: 1,
    backgroundColor: '#B89C4C',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  primaryButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#fff',
  },

  // Offer Remarks styles
  offerRemarksContainer: {
    padding: 16,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  offerRemarksLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 8,
  },
  offerRemarksInput: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    color: '#111827',
    backgroundColor: '#F9FAFB',
    minHeight: 100,
  },

  // Offer Negotiation styles
  offerNegotiationContainer: {
    backgroundColor: '#FFFFFF',
    flex: 1,
  },
  section: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  searchPropertiesButton: {
    backgroundColor: '#10B981',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  searchPropertiesButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#fff',
  },
});
