import React, { useState, useEffect, useMemo } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator, Platform } from 'react-native';
import { useLocalSearchParams, Stack, router } from 'expo-router';
import { useQuery, useMutation } from '@tanstack/react-query';
import { ArrowLeft, User, Settings, FileText, Clock, Calendar, Phone, Mail, MapPin, Briefcase, Flag, Edit3 } from 'lucide-react-native';
import { fetchLead, fetchLeadStatuses, fetchUsers, updateLeadAssignment, api } from '@/lib/api';
import Button from '@/components/Button';
import FormInput from '@/components/FormInput';
import Dropdown from '@/components/Dropdown';
import RequestModal from '@/components/RequestModal';
import RatingAlertModal from '@/components/RatingAlertModal';
import UserAvatar from '@/components/UserAvatar';
import DateTimePicker from '@react-native-community/datetimepicker';

export default function EditLead() {
  const { id } = useLocalSearchParams();
  const leadId = typeof id === 'string' ? parseInt(id, 10) : 0;
  const [showRequestModal, setShowRequestModal] = useState(false);
  const [showRatingModal, setShowRatingModal] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);

  // Local state for current status display
  const [currentDisplayStatus, setCurrentDisplayStatus] = useState<any>(null);
  const [currentDisplayRating, setCurrentDisplayRating] = useState<string>('');


  // Contact form state (read-only now, but keeping for consistency)
  const [contactForm, setContactForm] = useState({
    name: '',
    mobile: '',
    email: '',
  });

  // Assignment form state
  const [assignmentForm, setAssignmentForm] = useState({
    assignedTo: '',
    status: '',
    rating: '',
  });

  // Request form state
  const [requestData, setRequestData] = useState({});

  // History and remarks state
  const [remarks, setRemarks] = useState('');
  const [lastContactDate, setLastContactDate] = useState('');

  const { data: lead, isLoading, error } = useQuery({
    queryKey: ['lead', leadId],
    queryFn: () => fetchLead(leadId),
    enabled: leadId > 0,
  });

  // Initialize form data when lead data is available
  useEffect(() => {
    if (lead) {
      // Initialize form with lead data
      setContactForm({
        name: lead.contact.name || '',
        mobile: lead.contact.mobile_1 || '',
        email: lead.contact.email_1 || '',
      });
      setAssignmentForm({
        assignedTo: lead.latest_assignment?.user?.id?.toString() || '',
        status: lead.lead_status_id?.toString() || '',
        rating: lead.rating || '',
      });

      // Initialize request data from lead details
      let leadRequest = null;
      try {
        if (lead.leads_request) {
          leadRequest = JSON.parse(lead.leads_request);
        }
      } catch (e) {
        console.error('Error parsing leads_request:', e);
      }

      if (leadRequest) {
        setRequestData({
          rentSale: leadRequest.ot || lead.filter_operation_type || '',
          locations: '', // Would need location mapping
          towerBuilding: '',
          propertyType: leadRequest.pt || [],
          parking: '',
          minArea: '',
          maxArea: '',
          furnishing: '',
          furnishingOffice: '',
          balcony: '',
          views: '',
          pantry: '',
          bathrooms: (leadRequest.be && Array.isArray(leadRequest.be)) ? leadRequest.be.join(', ') : '',
          priceMin: lead.filter_budget_min?.toString() || '',
          priceMax: lead.filter_budget_max?.toString() || '',
          amenities: '',
          exclusiveProperty: false,
          marketingPlatforms: '',
          decisionDate: '',
          inquiredRefNo: lead.inquired_ref_no || '',
          specificRequirements: lead.requirements || '',
        });
      } else {
        // Initialize with basic lead data if no parsed request
        setRequestData({
          rentSale: lead.filter_operation_type || '',
          locations: '',
          towerBuilding: '',
          propertyType: [],
          parking: '',
          minArea: '',
          maxArea: '',
          furnishing: '',
          furnishingOffice: '',
          balcony: '',
          views: '',
          pantry: '',
          bathrooms: '',
          priceMin: lead.filter_budget_min?.toString() || '',
          priceMax: lead.filter_budget_max?.toString() || '',
          amenities: '',
          exclusiveProperty: false,
          marketingPlatforms: '',
          decisionDate: '',
          inquiredRefNo: lead.inquired_ref_no || '',
          specificRequirements: lead.requirements || '',
        });
      }

      setRemarks(lead.requirements || '');
      setLastContactDate(lead.last_call_timing ? new Date(lead.last_call_timing).toISOString().split('T')[0] : '');

      // Set current display status - find the status object from leadStatuses
      const statusObj = leadStatuses.find(s => s.id.toString() === lead.lead_status_id?.toString());

      // Check if this is a NEW status (common IDs for NEW status are 11, 1, or specific patterns)
      const isNewStatus = !statusObj && (
        lead.lead_status_id === 11 || // Common NEW status ID
        lead.lead_status_id === 1 ||  // Another common NEW status ID
        !lead.lead_status_id ||       // No status set
        lead.lead_status_id === null
      );

      if (isNewStatus) {
        // Create a mock status object for NEW
        setCurrentDisplayStatus({
          id: lead.lead_status_id || 11,
          name: 'NEW',
          background_color: getNewStatusColor()
        });
      } else {
        setCurrentDisplayStatus(statusObj);
      }

      // Debug logging
      if (!statusObj && lead.lead_status_id) {
        console.log('Status not found in leadStatuses, lead_status_id:', lead.lead_status_id);
        console.log('Available statuses:', leadStatuses.map(s => ({ id: s.id, name: s.name })));
      }

      // Set current display rating
      setCurrentDisplayRating(lead.rating || '');
    }
  }, [lead]);

  // Fetch lead statuses for dropdown
  const { data: leadStatuses = [] } = useQuery({
    queryKey: ['leadStatuses'],
    queryFn: fetchLeadStatuses,
  });

  // Fetch users for assignment dropdown
  const { data: users = [] } = useQuery({
    queryKey: ['users'],
    queryFn: fetchUsers,
  });

  // Rating options
  const ratingOptions = [
    { id: 'A', label: 'A - Excellent' },
    { id: 'B', label: 'B - Good' },
    { id: 'C', label: 'C - Average' },
    { id: 'D', label: 'D - Below Average' },
    { id: 'E', label: 'E - Poor' },
    { id: 'F', label: 'F - Very Poor' },
  ];

  // Get rating color function
  const getRatingColor = (rating: string) => {
    switch (rating) {
      case 'A':
        return '#22c55e';
      case 'B':
        return '#3b82f6';
      case 'C':
        return '#f59e0b';
      case 'D':
        return '#fb923c';
      case 'E':
        return '#ef4444';
      case 'F':
        return '#991b1b';
      default:
        return '#6b7280';
    }
  };

  // Get status color function for NEW status
  const getNewStatusColor = () => {
    // Culoarea pentru status NEW - de obicei verde deschis sau albastru
    return '#10b981'; // Verde emerald pentru status nou
  };

  // Date picker functions
  const handleDatePress = () => {
    setShowDatePicker(true);
  };

  const handleDateChange = (event: any, selectedDate?: Date) => {
    setShowDatePicker(false);

    if (selectedDate) {
      const formattedDate = selectedDate.toISOString().split('T')[0];
      setLastContactDate(formattedDate);
    }
  };

  // Transform users data for dropdown and ensure current assigned user is included
  const userOptions = useMemo(() => {
    const options = users.map(user => ({
      id: user.id.toString(),
      label: user.name,
      value: user
    }));

    // Always include current assigned user if available
    if (lead?.latest_assignment?.user) {
      const currentUser = lead.latest_assignment.user;
      const currentUserExists = options.some(option => option.id === currentUser.id.toString());

      if (!currentUserExists) {
        options.unshift({
          id: currentUser.id.toString(),
          label: currentUser.name,
          value: currentUser
        });
      }
    }

    // If no users from API and no current user, add a placeholder
    if (options.length === 0) {
      return [{
        id: 'no-agents',
        label: 'No agents available',
        value: null
      }];
    }

    return options;
  }, [users, lead?.latest_assignment?.user]);

  // Save mutation
  const { mutate: saveLead, isPending: isSaving } = useMutation({
    mutationFn: async () => {
      const promises = [];

      // Check if assignment changed
      const currentAssignedUserId = lead?.latest_assignment?.user?.id?.toString();

      if (assignmentForm.assignedTo && assignmentForm.assignedTo !== currentAssignedUserId) {
        promises.push(updateLeadAssignment(leadId, parseInt(assignmentForm.assignedTo)));
      }

      // Update other lead data
      const updateData = {
        status_id: assignmentForm.status ? parseInt(assignmentForm.status) : undefined,
        rating: assignmentForm.rating || undefined,
        remarks: remarks,
        last_contact_date: lastContactDate,
        request_data: requestData,
        requirements: remarks, // Update requirements field
      };

      // Remove undefined values
      Object.keys(updateData).forEach(key => {
        if (updateData[key] === undefined) {
          delete updateData[key];
        }
      });

      if (Object.keys(updateData).length > 0) {
        promises.push(api.put(`/leads/${leadId}`, updateData));
      }

      // Execute all updates
      const results = await Promise.all(promises);
      return results;
    },
    onSuccess: () => {
      router.back();
    },
    onError: (error) => {
      console.error('Error saving lead:', error);
      // Handle error (show toast, etc.)
    },
  });

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#B89C4C" />
      </View>
    );
  }

  if (error || !lead) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Failed to load lead details</Text>
        <TouchableOpacity style={styles.retryButton} onPress={() => router.back()}>
          <Text style={styles.retryButtonText}>Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <>
      <Stack.Screen
        options={{
          headerShown: true,
          header: () => (
            <View style={styles.header}>
              <TouchableOpacity
                style={styles.backButton}
                onPress={() => router.back()}
              >
                <ArrowLeft size={24} color="#111827" />
              </TouchableOpacity>
              <View style={styles.headerContent}>
                <Text style={styles.headerTitle}>Edit Lead</Text>
              </View>
            </View>
          ),
        }}
      />

      <ScrollView style={styles.container}>
        {/* Contact Details Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <User size={20} color="#B89C4C" />
            <Text style={styles.sectionTitle}>Contact Details</Text>
            <TouchableOpacity
              style={styles.editContactButton}
              onPress={() => {
                router.push({
                  pathname: '/leads/create/select-contact',
                  params: {
                    leadId: leadId.toString(),
                    mode: 'edit'
                  }
                });
              }}
            >
              <Edit3 size={18} color="#B89C4C" />
              <Text style={styles.editContactText}>Change</Text>
            </TouchableOpacity>
          </View>
          <View style={styles.sectionContent}>
            <View style={styles.contactHeader}>
              <UserAvatar
                imageUrl={lead.contact.profile_image}
                name={lead.contact.name}
                size={60}
                fontSize={24}
              />
              <View style={styles.contactHeaderInfo}>
                <Text style={styles.contactName}>{lead.contact.name}</Text>
                {lead.contact.company_name && (
                  <Text style={styles.contactCompany}>{lead.contact.company_name}</Text>
                )}
              </View>
            </View>

            <View style={styles.contactInfo}>
              {lead.contact.mobile_1 && (
                <View style={styles.infoItem}>
                  <Phone size={18} color="#6B7280" />
                  <Text style={styles.infoText}>
                    {lead.contact.prefix_mobile_1} {lead.contact.mobile_1}
                  </Text>
                </View>
              )}

              {lead.contact.mobile_2 && (
                <View style={styles.infoItem}>
                  <Phone size={18} color="#6B7280" />
                  <Text style={styles.infoText}>
                    {lead.contact.prefix_mobile_2} {lead.contact.mobile_2}
                  </Text>
                </View>
              )}

              {lead.contact.email_1 && (
                <View style={styles.infoItem}>
                  <Mail size={18} color="#6B7280" />
                  <Text style={styles.infoText}>{lead.contact.email_1}</Text>
                </View>
              )}

              {lead.contact.email_2 && (
                <View style={styles.infoItem}>
                  <Mail size={18} color="#6B7280" />
                  <Text style={styles.infoText}>{lead.contact.email_2}</Text>
                </View>
              )}

              {lead.contact.country && (
                <View style={styles.infoItem}>
                  <Flag size={18} color="#6B7280" />
                  <Text style={styles.infoText}>{lead.contact.country}</Text>
                </View>
              )}

              {lead.contact.address && (
                <View style={styles.infoItem}>
                  <MapPin size={18} color="#6B7280" />
                  <Text style={styles.infoText}>{lead.contact.address}</Text>
                </View>
              )}
            </View>
          </View>
        </View>

        {/* Assignment Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Settings size={20} color="#B89C4C" />
            <Text style={styles.sectionTitle}>Assignment</Text>
          </View>
          <View style={styles.sectionContent}>
            <Dropdown
              label="Assigned to"
              options={userOptions}
              selectedValue={assignmentForm.assignedTo}
              onSelect={(option) => setAssignmentForm(prev => ({ ...prev, assignedTo: option.id.toString() }))}
              placeholder="Select agent"
              searchable={true}
              searchPlaceholder="Search agents..."
            />
            {/* Current Status Info */}
            <View style={styles.currentAssignment}>
              <Text style={styles.currentAssignmentLabel}>Current status:</Text>
              <View style={styles.currentAssignmentInfo}>
                {currentDisplayStatus ? (
                  <>
                    <View
                      style={[
                        styles.statusIndicator,
                        { backgroundColor: currentDisplayStatus.background_color }
                      ]}
                    />
                    <Text style={styles.currentAssignmentName}>{currentDisplayStatus.name.replace(/_/g, ' ')}</Text>
                  </>
                ) : (
                  <>
                    <View
                      style={[
                        styles.statusIndicator,
                        { backgroundColor: getNewStatusColor() }
                      ]}
                    />
                    <Text style={styles.currentAssignmentName}>No status set</Text>
                  </>
                )}
                <TouchableOpacity
                  style={styles.editButton}
                  onPress={() => {
                    router.push({
                      pathname: '/leads/edit-status',
                      params: {
                        leadId: leadId.toString(),
                        currentStatus: assignmentForm.status
                      }
                    });
                  }}
                >
                  <Edit3 size={16} color="#6B7280" />
                </TouchableOpacity>
              </View>
            </View>

            {/* Current Rating Display */}
            <View style={styles.currentAssignment}>
              <Text style={styles.currentAssignmentLabel}>Current rating:</Text>
              <View style={styles.currentAssignmentInfo}>
                {currentDisplayRating ? (
                  <>
                    <View
                      style={[
                        styles.statusIndicator,
                        { backgroundColor: getRatingColor(currentDisplayRating) }
                      ]}
                    />
                    <Text style={styles.currentAssignmentName}>Rating {currentDisplayRating}</Text>
                  </>
                ) : (
                  <Text style={styles.currentAssignmentName}>No rating set</Text>
                )}
                <TouchableOpacity
                  style={styles.editButton}
                  onPress={() => setShowRatingModal(true)}
                >
                  <Edit3 size={16} color="#6B7280" />
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </View>

        {/* Request Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <FileText size={20} color="#B89C4C" />
            <Text style={styles.sectionTitle}>Request</Text>
          </View>
          <View style={styles.sectionContent}>
            <TouchableOpacity
              style={styles.requestButton}
              onPress={() => setShowRequestModal(true)}
            >
              <Text style={styles.requestButtonText}>Edit Request Details</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* History Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Clock size={20} color="#B89C4C" />
            <Text style={styles.sectionTitle}>History</Text>
          </View>
          <View style={styles.sectionContent}>
            {lead?.operation_history && lead.operation_history.length > 0 ? (
              <View style={styles.historyContainer}>
                {lead.operation_history.slice(0, 5).map((item, index) => (
                  <View key={item.id} style={styles.historyItem}>
                    <View style={styles.historyDot} />
                    <View style={styles.historyContent}>
                      <Text style={styles.historyText}>{item.content}</Text>
                      <Text style={styles.historyDate}>
                        {new Date(item.created_at).toLocaleDateString('en-GB', {
                          day: '2-digit',
                          month: 'short',
                          year: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit'
                        })} - IT Dev
                      </Text>
                    </View>
                  </View>
                ))}
              </View>
            ) : (
              <Text style={styles.placeholder}>No history available</Text>
            )}
          </View>
        </View>

        {/* Remarks Section */}
        <View style={styles.section}>
          <View style={styles.sectionContent}>
            <FormInput
              label="Remarks"
              value={remarks}
              onChangeText={setRemarks}
              placeholder="Add remarks or notes about this lead..."
              multiline
              numberOfLines={4}
            />
          </View>
        </View>

        {/* Last Contact Date Section */}
        <View style={styles.section}>
          <View style={styles.sectionContent}>
            <View style={styles.dateInputContainer}>
              <Text style={styles.dateLabel}>Last Contact Date</Text>
              <View style={styles.dateInputWrapper}>
                <FormInput
                  value={lastContactDate}
                  onChangeText={setLastContactDate}
                  placeholder="dd.mm.yyyy"
                />
                <TouchableOpacity
                  style={styles.calendarButton}
                  onPress={handleDatePress}
                >
                  <Calendar size={20} color="#6B7280" />
                </TouchableOpacity>
              </View>
              {/* Inline Date Picker */}
              {showDatePicker && (
                <View style={styles.inlineDatePicker}>
                  <DateTimePicker
                    value={(() => {
                      try {
                        return lastContactDate ? new Date(lastContactDate) : new Date();
                      } catch (error) {
                        return new Date();
                      }
                    })()}
                    mode="date"
                    display="default"
                    onChange={handleDateChange}
                  />
                </View>
              )}
            </View>
          </View>
        </View>
      </ScrollView>

      {/* Save Button */}
      <View style={styles.footer}>
        <View style={styles.buttonRow}>
          <TouchableOpacity
            style={styles.cancelButton}
            onPress={() => router.back()}
            disabled={isSaving}
          >
            <Text style={styles.cancelButtonText}>Cancel</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.updateButton, isSaving && styles.updateButtonDisabled]}
            onPress={() => saveLead()}
            disabled={isSaving}
          >
            <Text style={styles.updateButtonText}>
              {isSaving ? "Updating..." : "Update"}
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Request Modal */}
      <RequestModal
        visible={showRequestModal}
        onClose={() => setShowRequestModal(false)}
        onSave={(data) => {
          setRequestData(data);
          console.log('Request data saved:', data);
        }}
        initialData={requestData}
      />

      {/* Status editing is now handled by navigation to edit-status screen */}

      {/* Rating Alert Modal */}
      <RatingAlertModal
        visible={showRatingModal}
        onClose={() => setShowRatingModal(false)}
        selectedRating={currentDisplayRating}
        onSelectRating={(ratingId: string) => {
          setCurrentDisplayRating(ratingId);
          setAssignmentForm(prev => ({ ...prev, rating: ratingId }));
        }}
      />

    </>
  );
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#EF4444',
    textAlign: 'center',
    marginBottom: 20,
  },
  retryButton: {
    backgroundColor: '#B89C4C',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    marginRight: 16,
  },
  headerContent: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#111827',
  },
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  section: {
    backgroundColor: '#fff',
    marginBottom: 12,
    borderRadius: 12,
    marginHorizontal: 16,
    marginTop: 16,
    overflow: 'hidden',
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
    gap: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    flex: 1,
  },
  sectionContent: {
    padding: 16,
  },
  placeholder: {
    fontSize: 14,
    color: '#6B7280',
    fontStyle: 'italic',
  },
  requestButton: {
    backgroundColor: '#F3F4F6',
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderStyle: 'dashed',
  },
  requestButtonText: {
    fontSize: 16,
    color: '#B89C4C',
    fontWeight: '500',
    textAlign: 'center',
  },
  footer: {
    padding: 16,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  historyContainer: {
    paddingLeft: 8,
  },
  historyItem: {
    flexDirection: 'row',
    marginBottom: 16,
    alignItems: 'flex-start',
  },
  historyDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#B89C4C',
    marginTop: 6,
    marginRight: 12,
  },
  historyContent: {
    flex: 1,
  },
  historyText: {
    fontSize: 14,
    color: '#374151',
    lineHeight: 20,
    marginBottom: 4,
  },
  historyDate: {
    fontSize: 12,
    color: '#6B7280',
  },
  dateInputContainer: {
    marginBottom: 16,
  },
  dateLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 8,
  },
  dateInputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
  },
  calendarButton: {
    position: 'absolute',
    right: 12,
    top: 12,
    padding: 4,
  },
  buttonRow: {
    flexDirection: 'row',
    gap: 12,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: '#F3F4F6',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#6B7280',
  },
  updateButton: {
    flex: 1,
    backgroundColor: '#3B82F6',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: 'center',
  },
  updateButtonDisabled: {
    backgroundColor: '#9CA3AF',
  },
  updateButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#fff',
  },
  contactHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    gap: 12,
  },
  contactHeaderInfo: {
    flex: 1,
  },
  contactName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 4,
  },
  contactCompany: {
    fontSize: 14,
    color: '#6B7280',
  },
  contactInfo: {
    gap: 12,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    paddingVertical: 4,
  },
  infoText: {
    fontSize: 16,
    color: '#374151',
    flex: 1,
  },
  currentAssignment: {
    backgroundColor: '#F9FAFB',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  currentAssignmentLabel: {
    fontSize: 12,
    color: '#6B7280',
    marginBottom: 8,
    fontWeight: '500',
  },
  currentAssignmentInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  editButton: {
    marginLeft: 'auto',
    padding: 4,
  },
  currentAssignmentName: {
    fontSize: 14,
    color: '#374151',
    fontWeight: '500',
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  statusSection: {
    marginBottom: 20,
  },
  statusLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 8,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#F9FAFB',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  statusInfo: {
    flex: 1,
  },
  statusDisplay: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusText: {
    fontSize: 16,
    color: '#374151',
    fontWeight: '500',
  },
  placeholderText: {
    fontSize: 16,
    color: '#9CA3AF',
  },
  // Inline date picker styles
  inlineDatePicker: {
    marginTop: 8,
    backgroundColor: 'transparent',
    padding: 0,
  },
  // Edit contact button styles
  editContactButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    backgroundColor: '#FEF3C7',
  },
  editContactText: {
    fontSize: 14,
    color: '#B89C4C',
    fontWeight: '500',
  },
});
