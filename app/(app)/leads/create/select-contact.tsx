import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Stack, router, useLocalSearchParams } from 'expo-router';
import { ArrowLeft } from 'lucide-react-native';
import { useMutation, useQueryClient, useQuery } from '@tanstack/react-query';
import { api } from '@/lib/api';
import Button from '@/components/Button';
import ContactSelector from '@/components/ContactSelector';

type Contact = {
  id: number;
  name: string;
  email_1: string | null;
  prefix_mobile_1: string | null;
  mobile_1: string | null;
  company_name: string | null;
};

export default function SelectContact() {
  const { leadId, mode, returnTo } = useLocalSearchParams();
  const queryClient = useQueryClient();
  const isEditMode = mode === 'edit' && leadId;
  const isDetailsMode = mode === 'details' && leadId;

  // Get current lead data to preserve existing values
  const { data: currentLead } = useQuery({
    queryKey: ['lead', leadId],
    queryFn: () => api.get(`/leads/${leadId}`).then(res => res.data),
    enabled: isEditMode && !!leadId,
  });

  // Mutation for updating lead contact
  const updateContactMutation = useMutation({
    mutationFn: async (contactId: number) => {
      console.log('Updating lead contact:', {
        leadId,
        contactId,
        url: `/leads/${leadId}`,
        payload: { contact_id: contactId }
      });

      try {
        // Send all required fields based on your PHP validation
        const payload = {
          contact_id: contactId,
          status_id: currentLead?.lead_status_id || 11,
          remarks: currentLead?.requirements || 'Contact updated',
          ad_type: currentLead?.ad_type || null,
          agent_id: currentLead?.latest_assignment?.user?.id || null,
          platform: currentLead?.platform_from || null,
          budget: null,
          request: {
            propertyTypes: [currentLead?.filter_property_type || 1],
            operationType: currentLead?.filter_operation_type || 'rent',
            amenities: [],
            bedrooms: [],
            bathrooms: '',
            balcony: '',
            kitchen: '',
            listingViews: '',
            marketingPlatforms: '',
            maxArea: '',
            maxPrice: currentLead?.filter_budget_max?.toString() || '',
            minArea: '',
            minPrice: currentLead?.filter_budget_min?.toString() || '',
            pantry: '',
            parking: '',
            requirements: currentLead?.requirements || '',
            isExclusiveOrHotDeal: false
          }
        };

        console.log('Sending PATCH payload:', payload);
        console.log('URL:', `/leads/${leadId}`);

        // Use PATCH method as specified in your routes
        const response = await api.patch(`/leads/${leadId}`, payload);
        console.log('Contact update success:', response.data);
        return response.data;
      } catch (error: any) {
        console.error('Contact update error details:', {
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data,
          url: error.config?.url,
          method: error.config?.method,
          message: error.message
        });
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['lead', leadId] });
      queryClient.invalidateQueries({ queryKey: ['leads'] });
      router.back();
    },
    onError: (error) => {
      console.error('Error updating lead contact:', error);
    },
  });

  const handleSelectContact = (contact: Contact) => {
    if (isEditMode) {
      // Update existing lead's contact immediately
      updateContactMutation.mutate(contact.id);
    } else if (isDetailsMode) {
      // Return to details screen with selected contact for local handling
      router.back();
      router.setParams({
        selectedContact: JSON.stringify(contact)
      });
    } else {
      // Return to the previous screen with the selected contact for new lead creation
      router.push({
        pathname: '/leads/create',
        params: {
          contact: JSON.stringify(contact)
        }
      });
    }
  };

  return (
    <>
      <Stack.Screen
        options={{
          presentation: 'modal',
          headerShown: true,
          header: () => (
            <View style={styles.header}>
              <Button
                variant="ghost"
                icon={<ArrowLeft size={24} color="#111827" />}
                onPress={() => router.back()}
              />
              <Text style={styles.headerTitle}>
                {isEditMode ? 'Change Contact' : 'Select Contact'}
              </Text>
            </View>
          ),
        }}
      />

      <View style={styles.container}>
        <ContactSelector onSelectContact={handleSelectContact} />
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
    padding: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
    gap: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#111827',
  },
});