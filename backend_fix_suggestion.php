<?php
// Suggested fix for your backend edit method
// Replace the problematic section in your edit method

public function edit(Request $request, $leadId)
{
    $validData = $request->validate([
        "contact_id" => 'required|exists:contacts,id',
        'ad_type' => 'nullable',
        'budget' => 'nullable',
        'agent_id' => 'nullable|exists:users,id',
        'platform' => 'nullable',
        'status_id' => 'exists:lead_status,id',
        'remarks' => 'required_unless:status,14,15,23',
        'request.propertyTypes' => 'required|array',
        'request.amenities' => 'array',
        'request.balcony' => '',
        'request.bathrooms' => '',
        'request.bedrooms' => '',
        'request.isExclusiveOrHotDeal' => '',
        'request.kitchen' => '',
        'request.listingViews' => '',
        'request.marketingPlatforms' => '',
        'request.maxArea' => '',
        'request.maxPrice' => '',
        'request.minArea' => '',
        'request.minPrice' => '',
        'request.pantry' => '',
        'request.parking' => '',
        'request.operationType' => [
            'required',
            Rule::in('rent', 'sale')
        ],
        'request.requirements' => '',
    ]);

    Log::info('API Lead edit data', $validData);

    $lead = Lead::find($leadId);
    $lead->contact_id = $validData['contact_id'];

    // Store original attributes for change detection
    $originalAttributes = $lead->getAttributes();

    $processableFilterMap = [
        'filter_operation_type' => 'operationType',
        'filter_bedrooms' => 'bedrooms',
        'filter_bathrooms' => 'bathrooms',
        'filter_min_area' => 'minArea',
        'filter_max_area' => 'maxArea',
        'filter_budget_min' => 'minPrice',
        'filter_budget_max' => 'maxPrice',
        'requirements' => 'requirements'
    ];

    // Set property type
    $lead->filter_property_type = $validData['request']['propertyTypes'][0];
    
    // Process filter mappings
    foreach ($processableFilterMap as $k => $v) {
        if (array_key_exists($v, $validData['request']) && !empty($validData['request'][$v])) {
            if (is_array($validData['request'][$v])) {
                if (!empty($validData['request'][$v][0])) {
                    $lead->$k = $validData['request'][$v][0];
                }
            } else {
                $lead->$k = $validData['request'][$v];
            }
        }
    }

    // Set operation type consistently
    $operationType = $validData['request']['operationType'];
    $lead->ad_type = $operationType;
    $lead->filter_operation_type = $operationType;
    
    $lead->created_by = auth()->user()->id;
    $lead->property_type_id = $validData['request']['propertyTypes'][0];
    $lead->lead_status_id = $validData['status_id'];
    
    $leadRequestArr = [
        'ba' => empty($validData['request']['bathrooms']) || count($validData['request']['bathrooms']) < 1 ? null : $validData['request']['bathrooms'][0],
    ];

    $processableArr = [
        'ot' => 'operationType',
        't' => 'propertyTypes',
        'a' => 'amenities',
        'be' => 'bedrooms',
        'view' => 'listingViews',
        'mpt' => 'marketingPlatforms',
        'af' => 'minArea',
        'at' => 'maxArea',
        'par' => 'parking',
        'bal' => 'balcony',
        'pf' => 'minPrice',
        'pt' => 'maxPrice',
        'ki' => 'kitchen',
        'pantry' => 'pantry',
        'is_exclusive' => 'isExclusiveOrHotDeal',
    ];

    foreach ($processableArr as $k => $v) {
        if (array_key_exists($v, $validData['request'])) {
            $leadRequestArr[$k] = $validData['request'][$v];
        }
    }

    $lead->leads_request = json_encode((object) $leadRequestArr);

    // Check for changes
    $currentAttributes = $lead->getAttributes();
    $hasChanges = false;
    $statusChanged = false;

    foreach ($currentAttributes as $key => $value) {
        if (isset($originalAttributes[$key]) && $originalAttributes[$key] != $value) {
            $hasChanges = true;
            if ($key === 'lead_status_id') {
                $statusChanged = true;
            }
        }
    }

    $lead->save();

    $this->operationHistoryService->addOperationHistory($lead, "Updated via mobile application");

    if (!empty($validData['remarks'])) {
        $this->operationHistoryService->addOperationHistory($lead, $validData['remarks']);
    }

    $action = LeadInteractionTracking::UPDATED;
    $this->authorizationService->leadInteractionsTracking($lead->id, $action, null);

    return response($lead, 200);
}
