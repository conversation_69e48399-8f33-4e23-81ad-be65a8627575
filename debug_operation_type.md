# Debug Operation Type Issue

## Problem
Nu poți selecta și salva operation type-ul în edit request details din lead screen.

## Flow-ul actual
1. Din lead screen (`[id].tsx`) apasă butonul de edit pentru Request Details
2. Navigheaz<PERSON> la `/leads/create/details` cu parametrii:
   - `leadId`: ID-ul lead-ului
   - `mode`: 'details'
   - `adType`: operation type-ul curent
   - `existingData`: datele existente JSON

3. În details screen se afișează AdTypeSelector cu operation type-ul curent
4. Poți selecta alt operation type
5. La salvare se trimite PATCH la `/leads/${leadId}` cu payload-ul

## Modificările făcute

### Frontend
1. **Adăugat debugging în details screen** pentru a vedea ce se trimite la backend
2. **Adăugat debugging în lead screen** pentru a vedea ce se afișează

### Backend (sugestie)
Problema ar putea fi în backend. Verifică:

1. **Validarea** - asigură-te că `request.operationType` este validat corect
2. **Salvarea** - verifică că ambele câmpuri se salvează:
   ```php
   $lead->ad_type = $validData['request']['operationType'];
   $lead->filter_operation_type = $validData['request']['operationType'];
   ```

## Testare
1. Pornește aplicația
2. Mergi la un lead
3. Apasă edit pe Request Details
4. Schimbă operation type-ul
5. Salvează
6. Verifică în consolă:
   - Ce se trimite la backend
   - Ce răspuns vine de la backend
   - Dacă se actualizează datele în lead screen

## Debugging logs
Verifică în consolă:
- "=== FINAL PAYLOAD ===" - ce se trimite
- "=== BACKEND RESPONSE ===" - ce vine înapoi
- "Lead data updated:" - dacă se actualizează lead-ul
- "Displaying operation type:" - ce se afișează
